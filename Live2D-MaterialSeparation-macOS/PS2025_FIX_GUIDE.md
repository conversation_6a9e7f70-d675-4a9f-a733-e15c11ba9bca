# 🔧 Photoshop 2025 & macOS Sequoia 修复指南

## 🎯 问题诊断

您遇到的问题是常见的CEP扩展兼容性问题。主要原因：

1. **Photoshop 2025路径检测** - 安装脚本未能找到PS2025
2. **CEP版本兼容性** - 需要更新的CEP运行时版本
3. **macOS Sequoia权限** - 新的安全策略影响

## 🚀 快速修复方案

### 方案1: 自动修复 (推荐)

```bash
# 1. 进入项目目录
cd "/Volumes/JOJOCsy/Ai探索/Cursor/拆图/Live2D-MaterialSeparation-macOS"

# 2. 添加执行权限
chmod +x *.sh

# 3. 运行PS2025专用修复脚本
./fix_ps2025.sh

# 4. 测试安装
./test_ps2025.sh
```

### 方案2: 诊断后修复

```bash
# 1. 运行诊断工具
./diagnose.sh

# 2. 根据诊断结果运行相应修复
./fix_ps2025.sh
```

### 方案3: 手动修复

如果自动脚本失败，按以下步骤手动操作：

#### 步骤1: 创建CEP目录
```bash
mkdir -p "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"
```

#### 步骤2: 复制插件文件
```bash
cp -R Source/Plugin/* "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/"
```

#### 步骤3: 设置权限
```bash
chmod -R 755 "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"
```

#### 步骤4: 启用CEP调试模式
```bash
# 为所有CEP版本创建调试配置
for version in 9 10 11 12; do
cat > "$HOME/Library/Preferences/com.adobe.CSXS.$version.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>LogLevel</key>
    <string>1</string>
    <key>PlayerDebugMode</key>
    <string>1</string>
</dict>
</plist>
EOF
done
```

## 🎮 在Photoshop中启用插件

### 1. 启用开发者模式
1. 打开 **Photoshop 2025**
2. 转到 **Photoshop > 首选项 > 插件**
3. 勾选 **"加载扩展面板"**
4. 点击 **确定**

### 2. 重启Photoshop
- 完全退出Photoshop (Cmd+Q)
- 重新启动Photoshop

### 3. 查找插件
1. 转到 **窗口 > 扩展功能**
2. 查找 **"Live2D Material Separation"**
3. 点击打开插件面板

## 🔍 故障排除

### 插件不显示在菜单中

**检查CEP日志：**
```bash
tail -f ~/Library/Logs/CSXS/CEP*.log
```

**常见错误和解决方案：**

1. **"Extension not found"**
   ```bash
   # 重新安装插件
   ./fix_ps2025.sh
   ```

2. **"Permission denied"**
   ```bash
   # 修复权限
   sudo chmod -R 755 "$HOME/Library/Application Support/Adobe/CEP/extensions"
   ```

3. **"CEP version mismatch"**
   ```bash
   # 更新manifest.xml中的CEP版本
   # 已在fix_ps2025.sh中自动处理
   ```

### 插件显示但无法工作

1. **检查Python环境：**
   ```bash
   cd Source/Python
   python3 -c "import material_separation; print('OK')"
   ```

2. **检查核心库：**
   ```bash
   ls -la Source/Core/MaterialSeparationCore.dylib
   ```

3. **重新构建：**
   ```bash
   ./build.sh
   ```

## 📋 验证安装成功

运行测试脚本：
```bash
./test_ps2025.sh
```

成功标志：
- ✅ 插件目录存在
- ✅ 关键文件完整
- ✅ CEP调试文件已创建
- ✅ Photoshop中可见插件菜单

## 🆘 仍然无法解决？

### 1. 收集诊断信息
```bash
./diagnose.sh > diagnosis.txt
```

### 2. 检查系统兼容性
- macOS版本：15.5 (Sequoia) ✅
- Photoshop版本：2025 ✅
- 架构：Apple Silicon推荐

### 3. 重置CEP环境
```bash
# 清理现有安装
rm -rf "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"
rm -f "$HOME/Library/Preferences/com.adobe.CSXS.*.plist"

# 重新安装
./fix_ps2025.sh
```

### 4. 替代安装位置
如果用户目录安装失败，尝试系统级安装：
```bash
sudo mkdir -p "/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"
sudo cp -R Source/Plugin/* "/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/"
sudo chmod -R 755 "/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"
```

## 📞 技术支持

如果以上方法都无法解决问题：

1. **提供诊断信息**：运行 `./diagnose.sh` 的完整输出
2. **CEP日志**：`~/Library/Logs/CSXS/CEP*.log` 的相关错误信息
3. **系统信息**：macOS版本、Photoshop版本、Mac型号

---

**重要提示**：确保在每次修改后完全重启Photoshop，CEP扩展只在启动时加载。
