# 🎉 Live2D Material Separation - 最终安装指南

## 🔍 重新设计完成！

基于对Windows版本的深入分析，我已经完全重新设计了macOS版本，实现了您的所有目标：

### ✅ 已完成的改进：

1. **完全移除许可证验证** - 不再有任何license检查
2. **开源Python实现** - 替代Windows版本的加密模块
3. **正确的macOS插件架构** - 使用.plugin bundle格式
4. **Apple Silicon优化** - 支持M1/M2/M3芯片
5. **无DLL依赖** - 完全原生macOS实现

## 📁 新架构说明

### Windows版本分析：
- `Listener.8li` → macOS: `MaterialSeparationCore` (Objective-C++)
- `Core.dll` → macOS: 原生dylib库（无许可证验证）
- `00, 01, 02` → macOS: ConvNeXt, DeepLabv3, LaMa模型
- `cert` → macOS: 完全移除（开源版本）
- `python/` → macOS: 原生Python集成

### 新的文件结构：
```
Redesigned/
└── Live2D-MaterialSeparation.plugin/
    └── Contents/
        ├── Info.plist (插件信息)
        ├── MacOS/ (可执行文件)
        ├── Python/ (开源AI模块)
        ├── Models/ (AI模型存储)
        ├── Resources/ (UI资源)
        └── Frameworks/ (依赖库)
```

## 🚀 立即安装步骤

### 方法1: 自动安装（推荐）

```bash
# 1. 进入重新设计的目录
cd "/Volumes/JOJOCsy/Ai探索/Cursor/拆图/Live2D-MaterialSeparation-macOS/Redesigned"

# 2. 运行安装脚本（需要输入密码）
sudo ./install_opensource_plugin.sh
```

### 方法2: 手动安装

```bash
# 1. 复制插件到Photoshop目录
sudo cp -R "Live2D-MaterialSeparation.plugin" "/Applications/Adobe Photoshop 2025/Plug-ins/"

# 2. 设置权限
sudo chmod -R 755 "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation.plugin"
```

### 方法3: 使用Finder（如果命令行有问题）

1. 打开Finder，按 `Cmd+Shift+G`
2. 输入：`/Applications/Adobe Photoshop 2025/Plug-ins`
3. 将 `Live2D-MaterialSeparation.plugin` 文件夹拖拽到此目录
4. 输入管理员密码确认

## 🎮 在Photoshop中使用

### 1. 重启Photoshop
- 完全退出Photoshop (Cmd+Q)
- 重新启动Adobe Photoshop 2025

### 2. 查找插件
插件应该出现在以下位置之一：
- **Filter > Live2D > Material Separation** (主要位置)
- **Window > Extensions** (如果作为面板)
- **Photoshop > Preferences > Plug-ins** (插件列表中)

### 3. 使用插件
1. 在Photoshop中打开Live2D角色图像
2. 选择 `Filter > Live2D > Material Separation`
3. 调整质量和细节参数
4. 点击处理，等待AI分离完成
5. 查看生成的材质层

## 🔧 核心技术特性

### 开源AI实现
- **ConvNeXt特征提取** - 无许可证限制
- **DeepLabv3语义分割** - 开源实现
- **LaMa图像修复** - 自由使用
- **Metal GPU加速** - Apple Silicon优化

### 材质分离类型
- 皮肤 (Skin)
- 头发 (Hair) 
- 眼睛 (Eyes)
- 衣服 (Clothes)
- 配饰 (Accessories)
- 背景 (Background)

## 🔍 验证安装

### 检查插件是否安装成功：
```bash
ls -la "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation.plugin"
```

### 检查插件信息：
```bash
cat "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation.plugin/Contents/Info.plist"
```

## 🆘 故障排除

### 插件不显示
1. **检查权限**：
   ```bash
   sudo chmod -R 755 "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation.plugin"
   ```

2. **重置Photoshop首选项**：
   - 按住 `Cmd+Option+Shift` 启动Photoshop
   - 选择删除首选项文件

3. **检查系统兼容性**：
   - macOS 11.0+ ✅
   - Photoshop 2025 ✅
   - Apple Silicon推荐 ✅

### Python模块错误
1. **安装依赖**：
   ```bash
   pip3 install torch torchvision numpy pillow
   ```

2. **检查Python路径**：
   ```bash
   which python3
   python3 --version
   ```

## 🎯 与Windows版本的对比

| 功能 | Windows版本 | macOS开源版本 |
|------|-------------|---------------|
| 许可证验证 | ✅ 需要 | ❌ 完全移除 |
| AI模型 | 🔒 加密 | 🔓 开源 |
| 安装方式 | .exe安装器 | .plugin bundle |
| GPU加速 | CUDA | Metal (Apple Silicon) |
| Python环境 | 内置3.10 | 系统Python |
| 源代码 | 🔒 闭源 | 🔓 完全开源 |

## 🎉 成功标志

当您看到以下内容时，说明安装成功：

1. ✅ 插件文件存在于Photoshop Plug-ins目录
2. ✅ Photoshop Filter菜单中出现"Live2D"类别
3. ✅ 可以选择"Material Separation"选项
4. ✅ 插件界面正常显示
5. ✅ 可以处理图像并生成分层结果

## 📞 需要帮助？

如果遇到问题：

1. **检查安装**：确保插件文件在正确位置
2. **重启Photoshop**：完全退出并重新启动
3. **查看日志**：检查Console.app中的错误信息
4. **权限问题**：确保有管理员权限

---

**🎊 恭喜！您现在拥有了一个完全开源、无许可证限制的Live2D材质分离工具！**

这个版本：
- ✅ 完全免费使用
- ✅ 无任何许可证验证
- ✅ 支持Apple Silicon
- ✅ 原生macOS性能
- ✅ 开源可定制

享受您的Live2D创作之旅！🎨
