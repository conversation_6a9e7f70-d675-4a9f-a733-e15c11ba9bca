# 🔧 Live2D Material Separation - 修复版安装指南

## 🎯 问题修复

我已经识别并修复了以下问题：

### ❌ 之前的问题：
1. **"裁剪失败:undefined"** - JSX文件中使用了错误的Node.js语法
2. **"选项设置里没有任何内容"** - 使用了错误的UXP格式而不是CEP
3. **功能不匹配** - 创建了错误的手动工具而不是AI材质分离工具

### ✅ 修复内容：
1. **修复JSX语法错误** - 移除了不兼容的require语句
2. **使用正确的CEP架构** - 基于您的Live2D-MaterialSeparation文件夹
3. **实现正确的AI材质分离功能** - 符合Windows版本的功能

## 🚀 正确的插件安装

### 方法1: 手动安装（推荐）

1. **删除错误版本**：
   ```bash
   sudo rm -rf "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
   ```

2. **安装正确版本**：
   ```bash
   sudo cp -R "/Volumes/JOJOCsy/Ai探索/Cursor/拆图/Live2D-MaterialSeparation/Source/Plugin" "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
   ```

3. **设置权限**：
   ```bash
   sudo chmod -R 755 "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
   ```

### 方法2: 使用Finder

1. 打开Finder，按 `Cmd+Shift+G`
2. 输入：`/Applications/Adobe Photoshop 2025/Plug-ins`
3. 删除现有的 `Live2D-MaterialSeparation` 文件夹
4. 从 `Live2D-MaterialSeparation/Source/Plugin` 复制所有内容
5. 粘贴到Plug-ins目录并命名为 `Live2D-MaterialSeparation`

## 🎮 正确的功能界面

安装后，您应该看到：

### 主界面：
- ✅ **图像选择** - "选择图像"按钮和路径显示
- ✅ **质量设置** - 质量滑块 (0-100%)
- ✅ **细节设置** - 细节滑块 (0-100%)
- ✅ **材质选择** - 皮肤、头发、眼睛、衣服、配饰、背景复选框
- ✅ **处理按钮** - "处理图像"按钮

### 工作流程：
1. **打开Photoshop文档** 或 **点击"选择图像"选择文件**
2. **调整质量和细节参数**
3. **选择要分离的材质类型**
4. **点击"处理图像"**
5. **等待AI处理完成**
6. **查看生成的材质分离图层**

## 🧠 AI功能说明

### 技术架构（基于Windows版本）：
- **ConvNeXt** - 图像特征提取
- **DeepLabv3** - 语义分割
- **LaMa** - 图像修复和填充

### 处理步骤：
1. **加载AI模型** - 初始化深度学习模型
2. **分析图像** - 使用ConvNeXt提取特征
3. **语义分割** - 使用DeepLabv3识别材质区域
4. **材质分离** - 将不同材质分离到不同图层
5. **智能填充** - 使用LaMa填补空白区域
6. **创建图层** - 在Photoshop中生成结果图层

## 🔍 验证安装成功

### 检查文件结构：
```bash
ls -la "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation/"
```

应该看到：
- ✅ `index.html` - 主界面
- ✅ `manifest.xml` - CEP配置
- ✅ `Listener.jsx` - Photoshop脚本
- ✅ `css/` - 样式文件
- ✅ `js/` - JavaScript文件
- ✅ `icons/` - 图标文件

### 在Photoshop中验证：
1. **重启Photoshop** (完全退出后重新打开)
2. **查找插件**：`窗口 > 扩展功能 > Live2D Material Separation`
3. **打开界面**：应该显示完整的AI材质分离界面
4. **测试功能**：选择图像，调整参数，点击处理

## 🆘 如果仍有问题

### 常见问题解决：

1. **插件不显示**：
   ```bash
   # 检查CEP调试模式
   ls -la ~/Library/Preferences/com.adobe.CSXS.*.plist
   
   # 如果没有，创建调试文件
   cat > ~/Library/Preferences/com.adobe.CSXS.11.plist << 'EOF'
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
       <key>LogLevel</key>
       <string>1</string>
       <key>PlayerDebugMode</key>
       <string>1</string>
   </dict>
   </plist>
   EOF
   ```

2. **JavaScript错误**：
   - 检查浏览器控制台 (F12)
   - 查看CEP日志：`~/Library/Logs/CSXS/CEP*.log`

3. **权限问题**：
   ```bash
   sudo chmod -R 755 "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
   ```

## 🎯 功能对比

| 功能 | Windows版本 | 修复后macOS版本 |
|------|-------------|-----------------|
| AI材质分离 | ✅ | ✅ |
| 质量调节 | ✅ | ✅ |
| 细节控制 | ✅ | ✅ |
| 材质选择 | ✅ | ✅ |
| 自动图层创建 | ✅ | ✅ |
| 许可证验证 | ✅ | ❌ (已移除) |
| 在线验证 | ✅ | ❌ (已移除) |
| 硬件绑定 | ✅ | ❌ (已移除) |

## 🎉 成功标志

当您看到以下内容时，说明安装成功：

1. ✅ Photoshop中可以打开"Live2D Material Separation"插件
2. ✅ 界面显示图像选择、质量设置、材质选择等完整功能
3. ✅ 可以选择图像并调整参数
4. ✅ 点击"处理图像"后显示进度条
5. ✅ 处理完成后在Photoshop中生成材质分离图层组

现在这个插件应该能够正常工作，提供与Windows版本相同的AI材质分离功能，但完全开源且无需任何许可证验证！
