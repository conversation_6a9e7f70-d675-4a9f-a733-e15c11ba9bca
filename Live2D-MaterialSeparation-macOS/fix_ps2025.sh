#!/bin/bash

# Live2D Material Separation Plugin for macOS
# Photoshop 2025 & macOS Sequoia Compatibility Fix
# Open Source Version - No License Verification

set -e

# Define colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
EXTENSION_ID="com.live2d.materialseparation"

# Print banner
echo -e "${BLUE}==================================================${NC}"
echo -e "${BLUE}  Live2D Material Separation - PS2025 Fix${NC}"
echo -e "${BLUE}  macOS Sequoia 15.5 Compatibility${NC}"
echo -e "${BLUE}==================================================${NC}"
echo ""

# Function to find Photoshop installation
find_photoshop() {
    echo -e "${YELLOW}Searching for Photoshop installation...${NC}"
    
    # Possible Photoshop locations
    PS_PATHS=(
        "/Applications/Adobe Photoshop 2025"
        "/Applications/Adobe Photoshop"
        "/Applications/Adobe Photoshop 2024"
        "/Applications/Adobe Photoshop CC 2025"
        "/Applications/Adobe Photoshop 2025 (Beta)"
    )
    
    for ps_path in "${PS_PATHS[@]}"; do
        if [ -d "$ps_path" ]; then
            echo -e "${GREEN}✓ Found Photoshop at: $ps_path${NC}"
            return 0
        fi
    done
    
    echo -e "${RED}✗ Photoshop not found in standard locations${NC}"
    return 1
}

# Function to check CEP support
check_cep_support() {
    echo -e "${YELLOW}Checking CEP support...${NC}"
    
    # Check for CEP directories
    CEP_DIRS=(
        "$HOME/Library/Application Support/Adobe/CEP/extensions"
        "/Library/Application Support/Adobe/CEP/extensions"
    )
    
    for cep_dir in "${CEP_DIRS[@]}"; do
        if [ -d "$cep_dir" ]; then
            echo -e "${GREEN}✓ CEP directory found: $cep_dir${NC}"
            return 0
        fi
    done
    
    echo -e "${YELLOW}⚠ CEP directories not found, creating...${NC}"
    mkdir -p "$HOME/Library/Application Support/Adobe/CEP/extensions"
    return 0
}

# Function to update manifest for PS2025
update_manifest() {
    echo -e "${YELLOW}Updating manifest for Photoshop 2025...${NC}"
    
    MANIFEST_FILE="$SCRIPT_DIR/Source/Plugin/manifest.xml"
    
    if [ ! -f "$MANIFEST_FILE" ]; then
        echo -e "${RED}✗ Manifest file not found: $MANIFEST_FILE${NC}"
        return 1
    fi
    
    # Create backup
    cp "$MANIFEST_FILE" "$MANIFEST_FILE.backup"
    
    # Update manifest with PS2025 compatibility
    cat > "$MANIFEST_FILE" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<ExtensionManifest Version="10.0" ExtensionBundleId="com.live2d.materialseparation" ExtensionBundleVersion="1.0.0"
				 ExtensionBundleName="Live2D Material Separation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<ExtensionList>
		<Extension Id="com.live2d.materialseparation" Version="1.0.0" />
	</ExtensionList>
	<ExecutionEnvironment>
		<HostList>
			<!-- Photoshop 2021-2025+ support -->
			<Host Name="PHSP" Version="[22.0,99.9]" />
			<Host Name="PHXS" Version="[22.0,99.9]" />
		</HostList>
		<LocaleList>
			<Locale Code="All" />
		</LocaleList>
		<RequiredRuntimeList>
			<!-- CEP 11+ for PS2025 -->
			<RequiredRuntime Name="CSXS" Version="11.0" />
		</RequiredRuntimeList>
	</ExecutionEnvironment>
	<DispatchInfoList>
		<Extension Id="com.live2d.materialseparation">
			<DispatchInfo>
				<Resources>
					<MainPath>./index.html</MainPath>
					<ScriptPath>./jsx/Listener.jsx</ScriptPath>
				</Resources>
				<Lifecycle>
					<AutoVisible>true</AutoVisible>
				</Lifecycle>
				<UI>
					<Type>Panel</Type>
					<Menu>Live2D Material Separation</Menu>
					<Geometry>
						<Size>
							<Height>600</Height>
							<Width>400</Width>
						</Size>
						<MinSize>
							<Height>400</Height>
							<Width>300</Width>
						</MinSize>
						<MaxSize>
							<Height>800</Height>
							<Width>600</Width>
						</MaxSize>
					</Geometry>
					<Icons>
						<Icon Type="Normal">./icons/icon_normal.png</Icon>
						<Icon Type="RollOver">./icons/icon_rollover.png</Icon>
						<Icon Type="DarkNormal">./icons/icon_normal_dark.png</Icon>
						<Icon Type="DarkRollOver">./icons/icon_rollover_dark.png</Icon>
					</Icons>
				</UI>
			</DispatchInfo>
		</Extension>
	</DispatchInfoList>
</ExtensionManifest>
EOF
    
    echo -e "${GREEN}✓ Manifest updated for PS2025${NC}"
    return 0
}

# Function to setup CEP debug mode
setup_cep_debug() {
    echo -e "${YELLOW}Setting up CEP debug mode...${NC}"
    
    # Create debug files for all CEP versions
    for version in 9 10 11 12; do
        PLIST_FILE="$HOME/Library/Preferences/com.adobe.CSXS.$version.plist"
        
        cat > "$PLIST_FILE" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>LogLevel</key>
	<string>1</string>
	<key>PlayerDebugMode</key>
	<string>1</string>
</dict>
</plist>
EOF
    done
    
    echo -e "${GREEN}✓ CEP debug mode enabled for versions 9-12${NC}"
}

# Function to install plugin
install_plugin() {
    echo -e "${YELLOW}Installing plugin to CEP directory...${NC}"
    
    CEP_PLUGIN_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions/$EXTENSION_ID"
    
    # Remove existing installation
    if [ -d "$CEP_PLUGIN_DIR" ]; then
        echo -e "${YELLOW}Removing existing installation...${NC}"
        rm -rf "$CEP_PLUGIN_DIR"
    fi
    
    # Create plugin directory
    mkdir -p "$CEP_PLUGIN_DIR"
    
    # Copy plugin files
    echo -e "${YELLOW}Copying plugin files...${NC}"
    cp -R "$SCRIPT_DIR/Source/Plugin/"* "$CEP_PLUGIN_DIR/"
    
    # Set permissions
    chmod -R 755 "$CEP_PLUGIN_DIR"
    
    echo -e "${GREEN}✓ Plugin installed to: $CEP_PLUGIN_DIR${NC}"
}

# Function to create test script
create_test_script() {
    echo -e "${YELLOW}Creating test script...${NC}"
    
    cat > "$SCRIPT_DIR/test_ps2025.sh" << 'EOF'
#!/bin/bash

echo "🧪 Testing Live2D Material Separation Plugin"
echo "============================================="

# Check if plugin is installed
PLUGIN_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"

if [ -d "$PLUGIN_DIR" ]; then
    echo "✅ Plugin directory exists"
    
    # Check key files
    if [ -f "$PLUGIN_DIR/index.html" ]; then
        echo "✅ Main HTML file found"
    else
        echo "❌ Main HTML file missing"
    fi
    
    if [ -f "$PLUGIN_DIR/manifest.xml" ]; then
        echo "✅ Manifest file found"
    else
        echo "❌ Manifest file missing"
    fi
    
    # Check CEP debug files
    for version in 9 10 11 12; do
        if [ -f "$HOME/Library/Preferences/com.adobe.CSXS.$version.plist" ]; then
            echo "✅ CEP $version debug file found"
        else
            echo "❌ CEP $version debug file missing"
        fi
    done
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Restart Photoshop completely"
    echo "2. Go to Window > Extensions"
    echo "3. Look for 'Live2D Material Separation'"
    echo "4. If not visible, check CEP logs:"
    echo "   tail -f ~/Library/Logs/CSXS/CEP*.log"
    
else
    echo "❌ Plugin not installed"
    echo "Run ./fix_ps2025.sh to install"
fi
EOF
    
    chmod +x "$SCRIPT_DIR/test_ps2025.sh"
    echo -e "${GREEN}✓ Test script created: test_ps2025.sh${NC}"
}

# Main execution
main() {
    echo -e "${YELLOW}Starting Photoshop 2025 compatibility fix...${NC}"
    
    # Run all fixes
    find_photoshop
    check_cep_support
    update_manifest
    setup_cep_debug
    install_plugin
    create_test_script
    
    # Final instructions
    echo -e "\n${GREEN}==================================================${NC}"
    echo -e "${GREEN}  Photoshop 2025 Fix Complete!${NC}"
    echo -e "${GREEN}==================================================${NC}"
    echo -e "\n${YELLOW}Important Steps:${NC}"
    echo -e "1. ${BLUE}Completely quit Photoshop${NC} (Cmd+Q)"
    echo -e "2. ${BLUE}Restart Photoshop${NC}"
    echo -e "3. ${BLUE}Go to Window > Extensions${NC}"
    echo -e "4. ${BLUE}Look for 'Live2D Material Separation'${NC}"
    echo -e "\n${YELLOW}If plugin still not visible:${NC}"
    echo -e "• Run: ${BLUE}./test_ps2025.sh${NC}"
    echo -e "• Check logs: ${BLUE}tail -f ~/Library/Logs/CSXS/CEP*.log${NC}"
    echo -e "• Ensure Developer Mode is enabled in PS preferences"
    echo -e "\n${GREEN}Plugin installed to:${NC}"
    echo -e "${BLUE}$HOME/Library/Application Support/Adobe/CEP/extensions/$EXTENSION_ID${NC}"
}

# Run main function
main
