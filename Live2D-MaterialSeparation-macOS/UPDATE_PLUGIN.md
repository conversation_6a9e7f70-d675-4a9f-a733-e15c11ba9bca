# 🎯 Live2D 材质分离工具 - 功能更新

## 🔄 重新设计完成！

根据您的详细需求，我已经完全重新设计了插件，现在它具备了正确的功能：

### ✅ 核心功能（基于您的实际需求）：

#### 🔪 裁剪按钮
- **功能**：从图像中裁剪选区对象，并自动补全背景
- **使用场景**：Spine2D动画前的图层分离
- **工作流程**：
  1. 使用Photoshop选择工具建立选区
  2. 点击"裁剪"按钮
  3. 自动创建裁剪图层和填充背景图层

#### 📈 扩展按钮
- **功能**：扩展选区内的透明区域像素
- **使用场景**：补全图层的透明部分
- **工作流程**：
  1. 使用Photoshop选择工具建立选区
  2. 点击"扩展"按钮
  3. 智能填充透明区域

### 🎛️ 选项设置：

1. **在新组中创建图层** ✅ (默认开启)
2. **使用图层蒙版** (可选)
3. **填充裁剪区域** ✅ (默认开启)
4. **扩展裁剪区域** (可选)

### 🌏 界面语言：
- ✅ **完全中文界面**
- ✅ **中文状态提示**
- ✅ **中文按钮和选项**

## 🚀 更新安装步骤

### 方法1: 手动更新（推荐）

1. **删除旧版本**：
   - 打开Finder，按 `Cmd+Shift+G`
   - 输入：`/Applications/Adobe Photoshop 2025/Plug-ins`
   - 删除现有的 `Live2D-MaterialSeparation` 文件夹

2. **安装新版本**：
   - 将 `Live2D-MaterialSeparation-macOS/UXP-Plugin` 文件夹
   - 拖拽到 `/Applications/Adobe Photoshop 2025/Plug-ins/`
   - 重命名为 `Live2D-MaterialSeparation`
   - 输入管理员密码确认

### 方法2: 终端更新

```bash
# 删除旧版本并安装新版本
sudo rm -rf "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
sudo cp -R "/Volumes/JOJOCsy/Ai探索/Cursor/拆图/Live2D-MaterialSeparation-macOS/UXP-Plugin" "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
sudo chmod -R 755 "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
```

## 🎮 使用指南

### 基本工作流程：

#### 裁剪功能（图层分离）：
1. **打开图像**：在Photoshop中打开需要处理的2D美术素材
2. **建立选区**：使用选择工具（矩形选框、套索、魔棒等）选择要分离的对象
3. **点击裁剪**：在插件面板中点击"🔪 裁剪"按钮
4. **自动处理**：
   - 创建新的裁剪图层（包含选区内容）
   - 自动填充原图层的空缺部分
   - 在新组中组织图层（如果启用）

#### 扩展功能（像素补全）：
1. **选择透明区域**：选择需要扩展填充的透明部分
2. **点击扩展**：在插件面板中点击"📈 扩展"按钮
3. **智能填充**：根据周围像素智能填充透明区域

### 🎯 适用场景：

#### Spine2D动画制作：
- ✅ **图层分离**：将单个图层分离为多个可动画图层
- ✅ **像素补全**：为动画运动预留足够的像素范围
- ✅ **快速处理**：比手工抠图和填充快得多
- ✅ **2D优化**：专门针对2D美术风格优化

#### 美术素材整理：
- ✅ **批量处理**：快速处理多个图层
- ✅ **保持质量**：保持2D美术的平面化特征
- ✅ **智能填充**：比Photoshop生成式填充更适合2D素材

## 🔍 验证更新

更新后，您应该看到：

1. ✅ **中文界面**：插件标题显示为"Live2D 材质分离工具"
2. ✅ **正确按钮**：显示"🔪 裁剪"和"📈 扩展"两个按钮
3. ✅ **中文选项**：所有选项都是中文描述
4. ✅ **中文状态**：状态提示显示为中文

## 🆘 如果更新后仍有问题

1. **完全重启Photoshop**：确保插件重新加载
2. **检查文件位置**：确保文件在正确的Plug-ins目录
3. **清除缓存**：删除Photoshop首选项文件重新启动
4. **权限检查**：确保插件文件有正确的读取权限

## 🎉 功能对比

| 功能 | 之前版本 | 更新后版本 |
|------|----------|------------|
| 界面语言 | 英文 | ✅ 中文 |
| 核心功能 | AI材质识别 | ✅ 裁剪+扩展 |
| 使用方式 | 自动处理 | ✅ 基于选区 |
| 适用场景 | 通用 | ✅ Spine2D专用 |
| 处理速度 | 较慢 | ✅ 快速 |
| 2D优化 | 无 | ✅ 专门优化 |

现在这个插件完全符合您的实际需求：
- 🎯 **专为Spine2D动画制作设计**
- 🎯 **基于选区的精确控制**
- 🎯 **快速的2D素材处理**
- 🎯 **中文界面和提示**

请更新插件并测试新功能！
