# 📋 Live2D Material Separation Plugin - 手动部署指南

由于自动化脚本可能遇到终端编码问题，这里提供详细的手动部署步骤。

## 🔧 手动部署步骤

### 步骤 1: 验证环境 ✅

您的系统已经满足基本要求：
- ✅ Python 3.10.10 (已确认)
- ✅ Xcode命令行工具 (已确认)
- ✅ macOS系统 (已确认)

### 步骤 2: 打开终端并导航到项目目录

```bash
# 打开终端应用程序
# 导航到项目目录
cd "/Volumes/JOJOCsy/Ai探索/Cursor/拆图/Live2D-MaterialSeparation-macOS"

# 验证当前目录
pwd
ls -la
```

### 步骤 3: 创建Python虚拟环境

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 验证虚拟环境
which python3
python3 --version
```

### 步骤 4: 安装Python依赖

```bash
# 确保在虚拟环境中
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装基础依赖
pip install numpy pillow

# 安装PyTorch (Apple Silicon优化版本)
pip install torch torchvision

# 安装其他依赖
pip install scipy matplotlib tqdm pyyaml requests
```

### 步骤 5: 测试Python模块

```bash
# 进入Python模块目录
cd Source/Python

# 测试模块导入
python3 -c "import material_separation; print('✅ 模块导入成功!')"

# 返回项目根目录
cd ../..
```

### 步骤 6: 构建核心库

```bash
# 进入核心库目录
cd Source/Core

# 检查Makefile
cat Makefile

# 构建库 (如果遇到问题，先跳过这步)
make clean
make

# 返回项目根目录
cd ../..
```

### 步骤 7: 准备Photoshop插件目录

```bash
# 创建CEP扩展目录
mkdir -p "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"

# 检查目录是否创建成功
ls -la "$HOME/Library/Application Support/Adobe/CEP/extensions/"
```

### 步骤 8: 复制插件文件

```bash
# 复制插件界面文件
cp -R Source/Plugin/* "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/"

# 创建Python模块链接
ln -sf "$(pwd)/Source/Python" "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/python"

# 创建模型目录
mkdir -p "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/models"
```

### 步骤 9: 设置CEP调试模式

```bash
# 创建CEP调试配置
mkdir -p "$HOME/Library/Preferences"

# 创建调试配置文件
cat > "$HOME/Library/Preferences/com.adobe.CSXS.10.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>LogLevel</key>
    <string>1</string>
    <key>PlayerDebugMode</key>
    <string>1</string>
</dict>
</plist>
EOF
```

### 步骤 10: 验证安装

```bash
# 检查插件文件是否正确复制
ls -la "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/"

# 检查关键文件
ls -la "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/index.html"
ls -la "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/manifest.xml"
```

## 🎯 在Photoshop中测试

### 启动Photoshop
1. 关闭Photoshop (如果正在运行)
2. 重新启动Photoshop
3. 等待完全加载

### 查找插件
1. 在Photoshop中，转到 `窗口` > `扩展功能`
2. 查找 `Live2D Material Separation`
3. 点击打开插件

### 如果插件不显示
1. 检查CEP调试日志：
   ```bash
   tail -f "$HOME/Library/Logs/CSXS/CEP*.log"
   ```

2. 验证插件目录权限：
   ```bash
   chmod -R 755 "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"
   ```

## 🔍 故障排除

### 问题1: Python模块导入失败
```bash
# 检查虚拟环境
source venv/bin/activate
pip list

# 重新安装依赖
pip install -r requirements.txt
```

### 问题2: 插件不显示
```bash
# 检查CEP扩展目录
ls -la "$HOME/Library/Application Support/Adobe/CEP/extensions/"

# 检查插件文件
ls -la "$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation/"
```

### 问题3: 构建失败
```bash
# 检查Xcode工具
xcode-select --install

# 检查Python开发头文件
python3-config --includes
```

## ✅ 成功标志

当您看到以下内容时，说明部署成功：

1. ✅ Python虚拟环境创建成功
2. ✅ 基础依赖安装完成
3. ✅ Python模块可以导入
4. ✅ 插件文件复制到CEP目录
5. ✅ Photoshop中可以看到插件菜单

## 📞 需要帮助？

如果遇到问题：
1. 检查每个步骤的输出
2. 查看错误消息
3. 检查文件权限
4. 重启Photoshop
5. 查看CEP日志文件

---

**提示**: 首次部署建议逐步执行，确认每个步骤都成功后再继续下一步。
