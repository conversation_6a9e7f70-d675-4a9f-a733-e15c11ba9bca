#!/bin/bash

# Live2D Material Separation - Open Source Installation Script
# Installs the completely license-free version to Photoshop

set -e

echo "🎉 Installing Live2D Material Separation - Open Source Version"
echo "============================================================="

# Check for Photoshop
PS_DIR="/Applications/Adobe Photoshop 2025"
if [ ! -d "$PS_DIR" ]; then
    echo "❌ Photoshop 2025 not found"
    exit 1
fi

echo "✅ Found Photoshop 2025"

# Install plugin
PLUGIN_SOURCE="./Live2D-MaterialSeparation.plugin"
PLUGIN_DEST="$PS_DIR/Plug-ins/Live2D-MaterialSeparation.plugin"

echo "📦 Installing plugin..."
sudo rm -rf "$PLUGIN_DEST"
sudo cp -R "$PLUGIN_SOURCE" "$PLUGIN_DEST"
sudo chmod -R 755 "$PLUGIN_DEST"

echo "✅ Plugin installed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Restart Photoshop completely"
echo "2. Look for 'Live2D' in the Filter menu"
echo "3. Select Filter > Live2D > Material Separation"
echo ""
echo "🎉 Enjoy your license-free Live2D material separation tool!"
