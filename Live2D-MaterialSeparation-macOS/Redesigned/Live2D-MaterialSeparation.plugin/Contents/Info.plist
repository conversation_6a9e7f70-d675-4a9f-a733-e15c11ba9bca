<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleIdentifier</key>
    <string>com.live2d.materialseparation.opensource</string>
    <key>CFBundleName</key>
    <string>Live2D Material Separation</string>
    <key>CFBundleDisplayName</key>
    <string>Live2D Material Separation (Open Source)</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleExecutable</key>
    <string>MaterialSeparationCore</string>
    <key>CFBundlePackageType</key>
    <string>8BFM</string>
    <key>CFBundleSignature</key>
    <string>8BIM</string>
    <key>NSPrincipalClass</key>
    <string>MaterialSeparationPlugin</string>
    <key>PhotoshopFilterCategory</key>
    <string>Live2D</string>
    <key>PhotoshopFilterName</key>
    <string>Material Separation</string>
    <key>LSMinimumSystemVersion</key>
    <string>11.0</string>
    <key>LSArchitecturePriority</key>
    <array>
        <string>arm64</string>
        <string>x86_64</string>
    </array>
    <key>NSHumanReadableCopyright</key>
    <string>Open Source - No License Required</string>
</dict>
</plist>
