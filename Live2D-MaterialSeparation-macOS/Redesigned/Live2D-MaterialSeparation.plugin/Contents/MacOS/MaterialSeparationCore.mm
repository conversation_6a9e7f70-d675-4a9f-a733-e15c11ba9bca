/**
 * Live2D Material Separation Plugin for macOS
 * Open Source Core Library - NO LICENSE VERIFICATION
 * Based on Windows version but completely license-free
 */

#import <Foundation/Foundation.h>
#import <Metal/Metal.h>
#import <MetalPerformanceShaders/MetalPerformanceShaders.h>
#include <Python.h>

@interface MaterialSeparationCore : NSObject

@property (nonatomic, strong) id<MTLDevice> device;
@property (nonatomic, strong) id<MTLCommandQueue> commandQueue;
@property (nonatomic, assign) PyObject* pythonModule;

// Core methods - NO LICENSE CHECKS
- (instancetype)init;
- (BOOL)loadModels:(NSString*)modelPath;
- (NSDictionary*)separateMaterials:(NSString*)imagePath 
                       withQuality:(float)quality 
                       detailLevel:(float)detailLevel;
- (void)cleanup;

@end

@implementation MaterialSeparationCore

- (instancetype)init {
    self = [super init];
    if (self) {
        NSLog(@"🎉 Live2D Material Separation - Open Source Version");
        NSLog(@"✅ No license verification required!");
        
        // Initialize Metal for GPU acceleration
        _device = MTLCreateSystemDefaultDevice();
        if (_device) {
            _commandQueue = [_device newCommandQueue];
            NSLog(@"✅ Metal GPU acceleration enabled");
        }
        
        // Initialize Python environment
        if (![self initializePython]) {
            NSLog(@"⚠️ Python initialization failed, using fallback");
        }
    }
    return self;
}

- (BOOL)initializePython {
    // Initialize Python without any license checks
    if (!Py_IsInitialized()) {
        Py_Initialize();
    }
    
    // Add our Python path
    PyRun_SimpleString("import sys");
    PyRun_SimpleString("sys.path.append('.')");
    
    // Import our material separation module
    _pythonModule = PyImport_ImportModule("material_separation_opensource");
    
    return (_pythonModule != NULL);
}

- (BOOL)loadModels:(NSString*)modelPath {
    NSLog(@"📦 Loading AI models from: %@", modelPath);
    
    // Load models without any license verification
    // This replaces the Windows version's license-protected model loading
    
    if (!_pythonModule) {
        NSLog(@"❌ Python module not initialized");
        return NO;
    }
    
    // Call Python function to load models
    PyObject* loadFunc = PyObject_GetAttrString(_pythonModule, "load_models");
    if (loadFunc && PyCallable_Check(loadFunc)) {
        PyObject* args = PyTuple_New(1);
        PyTuple_SetItem(args, 0, PyUnicode_FromString([modelPath UTF8String]));
        
        PyObject* result = PyObject_CallObject(loadFunc, args);
        
        Py_DECREF(args);
        Py_DECREF(loadFunc);
        
        if (result) {
            BOOL success = PyObject_IsTrue(result);
            Py_DECREF(result);
            return success;
        }
    }
    
    return NO;
}

- (NSDictionary*)separateMaterials:(NSString*)imagePath 
                       withQuality:(float)quality 
                       detailLevel:(float)detailLevel {
    
    NSLog(@"🎨 Processing image: %@", imagePath);
    NSLog(@"⚙️ Quality: %.1f%%, Detail: %.1f%%", quality * 100, detailLevel * 100);
    
    if (!_pythonModule) {
        NSLog(@"❌ Python module not available");
        return nil;
    }
    
    // Call Python processing function
    PyObject* processFunc = PyObject_GetAttrString(_pythonModule, "separate_materials");
    if (processFunc && PyCallable_Check(processFunc)) {
        PyObject* args = PyTuple_New(3);
        PyTuple_SetItem(args, 0, PyUnicode_FromString([imagePath UTF8String]));
        PyTuple_SetItem(args, 1, PyFloat_FromDouble(quality));
        PyTuple_SetItem(args, 2, PyFloat_FromDouble(detailLevel));
        
        PyObject* result = PyObject_CallObject(processFunc, args);
        
        Py_DECREF(args);
        Py_DECREF(processFunc);
        
        if (result) {
            // Convert Python result to NSDictionary
            // This would contain the separated material layers
            NSMutableDictionary* resultDict = [NSMutableDictionary dictionary];
            
            // For now, return success status
            [resultDict setObject:@YES forKey:@"success"];
            [resultDict setObject:@"Material separation completed" forKey:@"message"];
            [resultDict setObject:@[@"skin", @"hair", @"eyes", @"clothes", @"background"] forKey:@"layers"];
            
            Py_DECREF(result);
            return resultDict;
        }
    }
    
    return nil;
}

- (void)cleanup {
    if (_pythonModule) {
        Py_DECREF(_pythonModule);
        _pythonModule = NULL;
    }
}

- (void)dealloc {
    [self cleanup];
}

@end

// C interface for Photoshop plugin
extern "C" {
    
    // Plugin entry point - NO LICENSE VERIFICATION
    OSErr PluginMain(const int16 selector, void* filterRecord, intptr_t* data, int16* result) {
        static MaterialSeparationCore* core = nil;
        
        switch (selector) {
            case filterSelectorAbout:
                // Show about dialog
                NSAlert* alert = [[NSAlert alloc] init];
                [alert setMessageText:@"Live2D Material Separation"];
                [alert setInformativeText:@"Open Source Version - No License Required\n\nAutomatically separates Live2D character materials using AI."];
                [alert addButtonWithTitle:@"OK"];
                [alert runModal];
                break;
                
            case filterSelectorParameters:
                // Show parameter dialog
                // This would show the UI for quality/detail settings
                break;
                
            case filterSelectorStart:
                // Initialize processing
                if (!core) {
                    core = [[MaterialSeparationCore alloc] init];
                }
                break;
                
            case filterSelectorContinue:
                // Process the image
                if (core) {
                    // Get current document path and process
                    NSString* imagePath = @"/tmp/current_image.psd"; // Placeholder
                    NSDictionary* result = [core separateMaterials:imagePath 
                                                       withQuality:0.8 
                                                       detailLevel:0.7];
                    
                    if (result && [[result objectForKey:@"success"] boolValue]) {
                        NSLog(@"✅ Material separation successful!");
                    }
                }
                break;
                
            case filterSelectorFinish:
                // Cleanup
                if (core) {
                    [core cleanup];
                    core = nil;
                }
                break;
        }
        
        return noErr;
    }
}
