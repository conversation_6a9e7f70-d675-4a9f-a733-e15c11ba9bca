#!/bin/bash

echo "🧪 Testing Live2D Material Separation Plugin"
echo "============================================="

# Check if plugin is installed
PLUGIN_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions/com.live2d.materialseparation"

if [ -d "$PLUGIN_DIR" ]; then
    echo "✅ Plugin directory exists"
    
    # Check key files
    if [ -f "$PLUGIN_DIR/index.html" ]; then
        echo "✅ Main HTML file found"
    else
        echo "❌ Main HTML file missing"
    fi
    
    if [ -f "$PLUGIN_DIR/manifest.xml" ]; then
        echo "✅ Manifest file found"
    else
        echo "❌ Manifest file missing"
    fi
    
    # Check CEP debug files
    for version in 9 10 11 12; do
        if [ -f "$HOME/Library/Preferences/com.adobe.CSXS.$version.plist" ]; then
            echo "✅ CEP $version debug file found"
        else
            echo "❌ CEP $version debug file missing"
        fi
    done
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Restart Photoshop completely"
    echo "2. Go to Window > Extensions"
    echo "3. Look for 'Live2D Material Separation'"
    echo "4. If not visible, check CEP logs:"
    echo "   tail -f ~/Library/Logs/CSXS/CEP*.log"
    
else
    echo "❌ Plugin not installed"
    echo "Run ./fix_ps2025.sh to install"
fi
