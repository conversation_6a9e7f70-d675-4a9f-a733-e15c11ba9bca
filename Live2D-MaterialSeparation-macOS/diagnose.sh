#!/bin/bash

# Live2D Material Separation Plugin - Diagnostic Tool
# Helps identify and fix common installation issues

set -e

# Define colors
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m"

echo -e "${BLUE}🔍 Live2D Material Separation - Diagnostic Tool${NC}"
echo -e "${BLUE}================================================${NC}"
echo ""

# Function to check status
check_status() {
    local description="$1"
    local condition="$2"
    local fix_hint="$3"
    
    if eval "$condition"; then
        echo -e "${GREEN}✅ $description${NC}"
        return 0
    else
        echo -e "${RED}❌ $description${NC}"
        if [ -n "$fix_hint" ]; then
            echo -e "   ${YELLOW}Fix: $fix_hint${NC}"
        fi
        return 1
    fi
}

echo -e "${YELLOW}1. Checking Photoshop Installation${NC}"
echo "----------------------------------------"

# Check for Photoshop 2025
PS_FOUND=false
PS_PATHS=(
    "/Applications/Adobe Photoshop 2025"
    "/Applications/Adobe Photoshop"
    "/Applications/Adobe Photoshop 2024"
    "/Applications/Adobe Photoshop CC 2025"
    "/Applications/Adobe Photoshop 2025 (Beta)"
)

for ps_path in "${PS_PATHS[@]}"; do
    if [ -d "$ps_path" ]; then
        echo -e "${GREEN}✅ Found Photoshop: $ps_path${NC}"
        PS_FOUND=true
        break
    fi
done

if [ "$PS_FOUND" = false ]; then
    echo -e "${RED}❌ Photoshop not found${NC}"
    echo -e "   ${YELLOW}Checked locations:${NC}"
    for ps_path in "${PS_PATHS[@]}"; do
        echo -e "   - $ps_path"
    done
fi

echo ""
echo -e "${YELLOW}2. Checking CEP Environment${NC}"
echo "----------------------------------------"

# Check CEP directories
CEP_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions"
check_status "CEP Extensions Directory" "[ -d '$CEP_DIR' ]" "mkdir -p '$CEP_DIR'"

# Check plugin installation
PLUGIN_DIR="$CEP_DIR/com.live2d.materialseparation"
check_status "Plugin Directory" "[ -d '$PLUGIN_DIR' ]" "Run ./fix_ps2025.sh"

if [ -d "$PLUGIN_DIR" ]; then
    check_status "Plugin manifest.xml" "[ -f '$PLUGIN_DIR/manifest.xml' ]" "Copy Source/Plugin/manifest.xml"
    check_status "Plugin index.html" "[ -f '$PLUGIN_DIR/index.html' ]" "Copy Source/Plugin/index.html"
fi

echo ""
echo -e "${YELLOW}3. Checking CEP Debug Configuration${NC}"
echo "----------------------------------------"

# Check CEP debug files
for version in 9 10 11 12; do
    PLIST_FILE="$HOME/Library/Preferences/com.adobe.CSXS.$version.plist"
    check_status "CEP $version Debug File" "[ -f '$PLIST_FILE' ]" "Run ./fix_ps2025.sh"
done

echo ""
echo -e "${YELLOW}4. Checking Developer Mode${NC}"
echo "----------------------------------------"

echo -e "${BLUE}ℹ️  Developer Mode must be enabled in Photoshop:${NC}"
echo -e "   1. Open Photoshop"
echo -e "   2. Go to Preferences > Plug-ins"
echo -e "   3. Enable 'Load Extension Panels'"
echo -e "   4. Restart Photoshop"

echo ""
echo -e "${YELLOW}5. Checking System Compatibility${NC}"
echo "----------------------------------------"

# Check macOS version
MACOS_VERSION=$(sw_vers -productVersion)
echo -e "${GREEN}✅ macOS Version: $MACOS_VERSION${NC}"

# Check if running on Apple Silicon
ARCH=$(uname -m)
if [ "$ARCH" = "arm64" ]; then
    echo -e "${GREEN}✅ Apple Silicon (M1/M2/M3) detected${NC}"
else
    echo -e "${YELLOW}⚠️  Intel Mac detected: $ARCH${NC}"
fi

echo ""
echo -e "${YELLOW}6. Checking Logs${NC}"
echo "----------------------------------------"

# Check for CEP logs
LOG_DIR="$HOME/Library/Logs/CSXS"
if [ -d "$LOG_DIR" ]; then
    echo -e "${GREEN}✅ CEP Log Directory exists${NC}"
    
    # Find recent log files
    RECENT_LOGS=$(find "$LOG_DIR" -name "CEP*.log" -mtime -1 2>/dev/null | head -3)
    if [ -n "$RECENT_LOGS" ]; then
        echo -e "${GREEN}✅ Recent CEP logs found:${NC}"
        echo "$RECENT_LOGS" | while read log_file; do
            echo -e "   - $log_file"
        done
        echo -e "\n${BLUE}💡 To view logs in real-time:${NC}"
        echo -e "   tail -f $LOG_DIR/CEP*.log"
    else
        echo -e "${YELLOW}⚠️  No recent CEP logs found${NC}"
    fi
else
    echo -e "${RED}❌ CEP Log Directory not found${NC}"
    echo -e "   ${YELLOW}Fix: mkdir -p '$LOG_DIR'${NC}"
fi

echo ""
echo -e "${YELLOW}7. Quick Fixes${NC}"
echo "----------------------------------------"

echo -e "${BLUE}🔧 Available Fix Scripts:${NC}"
echo -e "   ${GREEN}./fix_ps2025.sh${NC}     - Complete PS2025 compatibility fix"
echo -e "   ${GREEN}./install_plugin.sh${NC} - Standard installation"
echo -e "   ${GREEN}./test_ps2025.sh${NC}    - Test plugin installation (run after fix)"

echo ""
echo -e "${YELLOW}8. Manual Installation Steps${NC}"
echo "----------------------------------------"

echo -e "${BLUE}If scripts fail, try manual installation:${NC}"
echo -e "1. Copy plugin files:"
echo -e "   cp -R Source/Plugin/* '$PLUGIN_DIR/'"
echo -e "2. Set permissions:"
echo -e "   chmod -R 755 '$PLUGIN_DIR'"
echo -e "3. Enable CEP debug mode:"
echo -e "   ./fix_ps2025.sh"
echo -e "4. Restart Photoshop completely"

echo ""
echo -e "${YELLOW}9. Troubleshooting Tips${NC}"
echo "----------------------------------------"

echo -e "${BLUE}Common Issues and Solutions:${NC}"
echo -e "• ${YELLOW}Plugin not visible in menu:${NC}"
echo -e "  - Ensure Developer Mode is enabled"
echo -e "  - Check CEP logs for errors"
echo -e "  - Try different CEP debug versions"
echo -e ""
echo -e "• ${YELLOW}Permission denied errors:${NC}"
echo -e "  - Run: sudo chmod -R 755 '$CEP_DIR'"
echo -e "  - Check System Preferences > Security"
echo -e ""
echo -e "• ${YELLOW}Plugin loads but doesn't work:${NC}"
echo -e "  - Check Python environment"
echo -e "  - Verify core library compilation"
echo -e "  - Check plugin logs"

echo ""
echo -e "${GREEN}==================================================${NC}"
echo -e "${GREEN}  Diagnostic Complete${NC}"
echo -e "${GREEN}==================================================${NC}"

# Summary
echo -e "\n${BLUE}📋 Summary:${NC}"
if [ "$PS_FOUND" = true ] && [ -d "$PLUGIN_DIR" ]; then
    echo -e "${GREEN}✅ Basic setup appears correct${NC}"
    echo -e "${YELLOW}Next: Restart Photoshop and check Window > Extensions${NC}"
else
    echo -e "${RED}❌ Issues found - run ./fix_ps2025.sh to fix${NC}"
fi

echo -e "\n${BLUE}🆘 Need help? Check the logs:${NC}"
echo -e "tail -f ~/Library/Logs/CSXS/CEP*.log"
