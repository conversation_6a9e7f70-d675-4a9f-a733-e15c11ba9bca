# 🚀 Live2D Material Separation Plugin - 快速开始指南

## 📋 前置要求

在开始之前，请确保您的系统满足以下要求：

- ✅ **macOS 11.0+** (Big Sur或更高版本)
- ✅ **Python 3.10+** 
- ✅ **Adobe Photoshop 2021+**
- ✅ **Xcode命令行工具**

## 🔧 快速安装 (推荐)

### 步骤 1: 检查系统环境
```bash
# 检查Python版本
python3 --version

# 检查Xcode工具
xcode-select -p

# 如果没有安装Xcode工具，运行：
# xcode-select --install
```

### 步骤 2: 运行测试脚本
```bash
cd Live2D-MaterialSeparation-macOS
chmod +x *.sh
./test_plugin.sh
```

### 步骤 3: 下载AI模型
```bash
./download_models.sh
```

### 步骤 4: 构建插件
```bash
./build.sh
```

### 步骤 5: 安装插件
```bash
./install_plugin.sh
```

### 步骤 6: 重启Photoshop
关闭并重新启动Adobe Photoshop

## 🎯 使用插件

1. **打开Photoshop**
2. **加载插件**: `窗口 > 扩展功能 > Live2D Material Separation`
3. **选择图像**: 点击"选择图像"或"使用当前文档"
4. **调整设置**: 
   - 质量级别 (0-100%)
   - 细节级别 (0-100%)
   - 选择要提取的材质类型
5. **开始处理**: 点击"处理图像"按钮
6. **等待完成**: 插件会自动生成分层PSD文件

## 🔍 故障排除

### 常见问题

**Q: 插件在Photoshop中不显示**
```bash
# 检查CEP扩展目录权限
ls -la ~/Library/Application\ Support/Adobe/CEP/extensions/

# 检查调试日志
tail -f ~/Library/Logs/CSXS/CEP*.log
```

**Q: Python模块导入失败**
```bash
# 重新安装依赖
cd Live2D-MaterialSeparation-macOS
source venv/bin/activate
pip install -r requirements.txt
```

**Q: 构建失败**
```bash
# 检查Xcode工具
xcode-select --install

# 清理并重新构建
cd Source/Core
make clean
make
```

### 调试模式

启用CEP调试模式：
```bash
# 创建调试配置
mkdir -p ~/Library/Preferences
cat > ~/Library/Preferences/com.adobe.CSXS.10.plist << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>LogLevel</key>
    <string>1</string>
    <key>PlayerDebugMode</key>
    <string>1</string>
</dict>
</plist>
EOF
```

## 📁 项目结构说明

```
Live2D-MaterialSeparation-macOS/
├── Source/
│   ├── Core/                 # Objective-C++核心库
│   ├── Plugin/              # Photoshop CEP扩展
│   └── Python/              # Python深度学习模块
├── Models/                  # AI模型文件
├── Build/                   # 构建输出
├── Tests/                   # 测试文件
├── build.sh                 # 构建脚本
├── install_plugin.sh        # 安装脚本
├── download_models.sh       # 模型下载脚本
├── test_plugin.sh          # 测试脚本
└── requirements.txt         # Python依赖
```

## 🧪 测试功能

### 基本功能测试
```bash
# 运行完整测试套件
./test_plugin.sh

# 测试Python模块
cd Source/Python
python3 material_separation.py test_image.jpg
```

### 性能测试
```bash
# 测试GPU加速
python3 -c "import torch; print('MPS available:', torch.backends.mps.is_available())"
```

## 🔄 更新和维护

### 更新模型
```bash
# 重新下载最新模型
rm -rf Models/*
./download_models.sh
```

### 更新依赖
```bash
# 更新Python依赖
pip install -r requirements.txt --upgrade
```

### 重新安装插件
```bash
# 完全重新安装
./build.sh
./install_plugin.sh
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志**: `~/Library/Logs/CSXS/CEP*.log`
2. **运行测试**: `./test_plugin.sh`
3. **检查文档**: 阅读 `README.md`
4. **社区支持**: 提交Issue到GitHub仓库

## 🎉 成功标志

当您看到以下内容时，说明安装成功：

- ✅ 测试脚本全部通过
- ✅ Photoshop中可以看到插件
- ✅ 插件界面正常显示
- ✅ 可以选择和处理图像

---

**提示**: 首次使用建议先用小尺寸的测试图像验证功能，确认一切正常后再处理大型项目文件。
