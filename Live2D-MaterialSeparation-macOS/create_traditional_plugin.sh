#!/bin/bash

# Live2D Material Separation Plugin for macOS
# Traditional Photoshop Plugin Creator
# Creates .plugin bundle for Photoshop Plug-ins directory

set -e

# Define colors
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m"

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PS_PLUGINS_DIR="/Applications/Adobe Photoshop 2025/Plug-ins"
PLUGIN_NAME="Live2D Material Separation.plugin"
PLUGIN_DIR="$PS_PLUGINS_DIR/$PLUGIN_NAME"

echo -e "${BLUE}🔧 Creating Traditional Photoshop Plugin${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""

# Check if Photoshop exists
if [ ! -d "/Applications/Adobe Photoshop 2025" ]; then
    echo -e "${RED}❌ Adobe Photoshop 2025 not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found Adobe Photoshop 2025${NC}"

# Function to create plugin bundle structure
create_plugin_bundle() {
    echo -e "${YELLOW}Creating plugin bundle structure...${NC}"
    
    # Remove existing plugin if it exists
    if [ -d "$PLUGIN_DIR" ]; then
        echo -e "${YELLOW}Removing existing plugin...${NC}"
        sudo rm -rf "$PLUGIN_DIR"
    fi
    
    # Create plugin bundle directory
    sudo mkdir -p "$PLUGIN_DIR/Contents/MacOS"
    sudo mkdir -p "$PLUGIN_DIR/Contents/Resources"
    sudo mkdir -p "$PLUGIN_DIR/Contents/Python"
    sudo mkdir -p "$PLUGIN_DIR/Contents/Models"
    
    echo -e "${GREEN}✅ Plugin bundle structure created${NC}"
}

# Function to create Info.plist
create_info_plist() {
    echo -e "${YELLOW}Creating Info.plist...${NC}"
    
    sudo tee "$PLUGIN_DIR/Contents/Info.plist" > /dev/null << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleIdentifier</key>
    <string>com.live2d.materialseparation</string>
    <key>CFBundleName</key>
    <string>Live2D Material Separation</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleExecutable</key>
    <string>MaterialSeparationCore</string>
    <key>CFBundlePackageType</key>
    <string>8BFM</string>
    <key>CFBundleSignature</key>
    <string>8BIM</string>
    <key>NSPrincipalClass</key>
    <string>MaterialSeparationPlugin</string>
    <key>PhotoshopFilterCategory</key>
    <string>Live2D</string>
    <key>PhotoshopFilterName</key>
    <string>Material Separation</string>
</dict>
</plist>
EOF
    
    echo -e "${GREEN}✅ Info.plist created${NC}"
}

# Function to copy core library
copy_core_library() {
    echo -e "${YELLOW}Copying core library...${NC}"
    
    # Build core library if it doesn't exist
    if [ ! -f "$SCRIPT_DIR/Source/Core/MaterialSeparationCore.dylib" ]; then
        echo -e "${YELLOW}Building core library...${NC}"
        cd "$SCRIPT_DIR/Source/Core"
        make clean || true
        make || {
            echo -e "${RED}❌ Failed to build core library${NC}"
            return 1
        }
        cd "$SCRIPT_DIR"
    fi
    
    # Copy core library
    sudo cp "$SCRIPT_DIR/Source/Core/MaterialSeparationCore.dylib" "$PLUGIN_DIR/Contents/MacOS/MaterialSeparationCore"
    
    echo -e "${GREEN}✅ Core library copied${NC}"
}

# Function to copy Python environment
copy_python_environment() {
    echo -e "${YELLOW}Setting up Python environment...${NC}"
    
    # Copy Python module
    sudo cp -R "$SCRIPT_DIR/Source/Python/"* "$PLUGIN_DIR/Contents/Python/"
    
    # Create Python launcher script
    sudo tee "$PLUGIN_DIR/Contents/MacOS/python_launcher.sh" > /dev/null << 'EOF'
#!/bin/bash
# Python launcher for Live2D Material Separation Plugin

PLUGIN_DIR="$(dirname "$(dirname "$0")")"
PYTHON_DIR="$PLUGIN_DIR/Python"
VENV_DIR="$PLUGIN_DIR/Python/venv"

# Create virtual environment if it doesn't exist
if [ ! -d "$VENV_DIR" ]; then
    python3 -m venv "$VENV_DIR"
    source "$VENV_DIR/bin/activate"
    pip install torch torchvision numpy pillow
else
    source "$VENV_DIR/bin/activate"
fi

# Run the Python module
python3 "$PYTHON_DIR/material_separation.py" "$@"
EOF
    
    sudo chmod +x "$PLUGIN_DIR/Contents/MacOS/python_launcher.sh"
    
    echo -e "${GREEN}✅ Python environment set up${NC}"
}

# Function to create plugin interface
create_plugin_interface() {
    echo -e "${YELLOW}Creating plugin interface...${NC}"
    
    # Copy UI resources
    sudo cp -R "$SCRIPT_DIR/Source/Plugin/css" "$PLUGIN_DIR/Contents/Resources/" 2>/dev/null || true
    sudo cp -R "$SCRIPT_DIR/Source/Plugin/js" "$PLUGIN_DIR/Contents/Resources/" 2>/dev/null || true
    sudo cp -R "$SCRIPT_DIR/Source/Plugin/icons" "$PLUGIN_DIR/Contents/Resources/" 2>/dev/null || true
    sudo cp "$SCRIPT_DIR/Source/Plugin/index.html" "$PLUGIN_DIR/Contents/Resources/" 2>/dev/null || true
    
    echo -e "${GREEN}✅ Plugin interface created${NC}"
}

# Function to set permissions
set_permissions() {
    echo -e "${YELLOW}Setting permissions...${NC}"
    
    sudo chown -R root:wheel "$PLUGIN_DIR"
    sudo chmod -R 755 "$PLUGIN_DIR"
    sudo chmod +x "$PLUGIN_DIR/Contents/MacOS/"*
    
    echo -e "${GREEN}✅ Permissions set${NC}"
}

# Function to create launcher script for testing
create_test_launcher() {
    echo -e "${YELLOW}Creating test launcher...${NC}"
    
    cat > "$SCRIPT_DIR/test_traditional_plugin.sh" << 'EOF'
#!/bin/bash

echo "🧪 Testing Traditional Photoshop Plugin"
echo "======================================"

PLUGIN_DIR="/Applications/Adobe Photoshop 2025/Plug-ins/Live2D Material Separation.plugin"

if [ -d "$PLUGIN_DIR" ]; then
    echo "✅ Plugin bundle exists"
    
    # Check structure
    echo "📁 Plugin structure:"
    ls -la "$PLUGIN_DIR/Contents/"
    
    # Check executable
    if [ -f "$PLUGIN_DIR/Contents/MacOS/MaterialSeparationCore" ]; then
        echo "✅ Core executable found"
    else
        echo "❌ Core executable missing"
    fi
    
    # Check Info.plist
    if [ -f "$PLUGIN_DIR/Contents/Info.plist" ]; then
        echo "✅ Info.plist found"
    else
        echo "❌ Info.plist missing"
    fi
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Restart Photoshop completely"
    echo "2. Look for 'Live2D' in the Filter menu"
    echo "3. Check Filter > Live2D > Material Separation"
    
else
    echo "❌ Plugin not found at: $PLUGIN_DIR"
    echo "Run ./create_traditional_plugin.sh to install"
fi
EOF
    
    chmod +x "$SCRIPT_DIR/test_traditional_plugin.sh"
    echo -e "${GREEN}✅ Test launcher created${NC}"
}

# Main execution
main() {
    echo -e "${YELLOW}Starting traditional plugin creation...${NC}"
    
    # Check for sudo access
    echo -e "${YELLOW}This script requires administrator access to install to Photoshop Plug-ins directory${NC}"
    sudo -v
    
    # Create plugin
    create_plugin_bundle
    create_info_plist
    copy_core_library
    copy_python_environment
    create_plugin_interface
    set_permissions
    create_test_launcher
    
    # Final message
    echo -e "\n${GREEN}==================================================${NC}"
    echo -e "${GREEN}  Traditional Plugin Created Successfully!${NC}"
    echo -e "${GREEN}==================================================${NC}"
    echo -e "\n${YELLOW}Plugin installed to:${NC}"
    echo -e "${BLUE}$PLUGIN_DIR${NC}"
    echo -e "\n${YELLOW}Next Steps:${NC}"
    echo -e "1. ${BLUE}Completely restart Photoshop${NC}"
    echo -e "2. ${BLUE}Look for 'Live2D' in Filter menu${NC}"
    echo -e "3. ${BLUE}Select Filter > Live2D > Material Separation${NC}"
    echo -e "\n${YELLOW}Test the installation:${NC}"
    echo -e "${BLUE}./test_traditional_plugin.sh${NC}"
    echo -e "\n${GREEN}If successful, the plugin should appear in Photoshop's Filter menu!${NC}"
}

# Run main function
main
EOF
