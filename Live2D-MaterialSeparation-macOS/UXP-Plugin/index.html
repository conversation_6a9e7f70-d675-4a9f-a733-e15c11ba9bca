<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Live2D Material Separation</title>
    <style>
        body {
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--uxp-host-background-color);
            color: var(--uxp-host-text-color);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 12px;
            opacity: 0.7;
        }

        .section {
            margin-bottom: 20px;
            padding: 12px;
            border: 1px solid var(--uxp-host-border-color);
            border-radius: 6px;
        }

        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .input-group {
            margin-bottom: 12px;
        }

        label {
            display: block;
            margin-bottom: 4px;
            font-size: 12px;
        }

        input[type="range"] {
            width: 100%;
            margin-bottom: 4px;
        }

        .slider-value {
            font-size: 11px;
            text-align: center;
            opacity: 0.8;
        }

        .checkbox-group {
            display: block;
            margin-bottom: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            margin-bottom: 8px;
            padding: 4px;
            background: rgba(255,255,255,0.05);
            border-radius: 4px;
        }

        .checkbox-item input {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        .checkbox-item label {
            cursor: pointer;
            flex: 1;
        }

        .button {
            width: 100%;
            padding: 12px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 8px;
        }

        .button:hover {
            background: #0052a3;
        }

        .button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .progress {
            width: 100%;
            height: 6px;
            background: var(--uxp-host-border-color);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background: #0066cc;
            width: 0%;
            transition: width 0.3s ease;
        }

        .status {
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
            min-height: 16px;
        }

        .success {
            color: #00aa00;
        }

        .error {
            color: #cc0000;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Live2D 材质分离工具</div>
        <div class="subtitle">开源版本 - 无需许可证</div>
    </div>

    <div class="section">
        <div class="section-title">主要功能</div>
        <div style="margin-bottom: 12px; font-size: 12px; color: #666;">
            请先使用Photoshop选择工具建立选区，然后点击下方按钮
        </div>

        <button class="button" id="cropBtn" style="background: #e74c3c; margin-bottom: 8px;">
            🔪 裁剪
        </button>
        <div style="font-size: 11px; margin-bottom: 12px; opacity: 0.8;">
            从图像中裁剪选区对象，并自动补全背景
        </div>

        <button class="button" id="expandBtn" style="background: #27ae60;">
            📈 扩展
        </button>
        <div style="font-size: 11px; margin-bottom: 12px; opacity: 0.8;">
            扩展选区内的透明区域像素
        </div>
    </div>

    <div class="section">
        <div class="section-title">选项设置</div>
        <div class="checkbox-group">
            <div class="checkbox-item">
                <input type="checkbox" id="createGroup" checked>
                <label for="createGroup">✅ 在新组中创建图层</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" id="useLayerMask">
                <label for="useLayerMask">🎭 使用图层蒙版</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" id="fillCropArea" checked>
                <label for="fillCropArea">🪄 填充裁剪区域 (魔术修复)</label>
            </div>
            <div class="checkbox-item">
                <input type="checkbox" id="expandCropArea">
                <label for="expandCropArea">📏 扩展裁剪区域</label>
            </div>
        </div>
    </div>
    <div class="progress" id="progress">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    <div class="status" id="status"></div>

    <script>
        // Live2D 材质分离工具 - UXP Plugin Script
        // 开源版本 - 无需许可证验证

        const { app, core } = require('photoshop');

        // UI Elements
        const cropBtn = document.getElementById('cropBtn');
        const expandBtn = document.getElementById('expandBtn');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');
        const status = document.getElementById('status');

        // 事件处理器将在DOMContentLoaded中绑定

        // 获取设置选项
        function getSettings() {
            return {
                createGroup: document.getElementById('createGroup').checked,
                useLayerMask: document.getElementById('useLayerMask').checked,
                fillCropArea: document.getElementById('fillCropArea').checked,
                expandCropArea: document.getElementById('expandCropArea').checked
            };
        }

        // 检查是否有选区
        async function checkSelection() {
            try {
                await core.executeAsModal(async () => {
                    const doc = app.activeDocument;
                    // 尝试获取选区边界，如果没有选区会抛出异常
                    const bounds = doc.selection.bounds;
                });
                return true;
            } catch (error) {
                return false;
            }
        }

        // 检查当前选区状态（同步版本）
        async function checkActiveSelection() {
            try {
                const result = await app.batchPlay([{
                    "_obj": "get",
                    "_target": [{"_ref": "property", "_property": "selection"}, {"_ref": "document", "_enum": "ordinal", "_value": "targetEnum"}]
                }], {});

                return result && result[0] && result[0].selection;
            } catch (error) {
                return false;
            }
        }

        // 裁剪处理函数 - 真正的对象分离和智能修复
        async function processCrop(settings) {
            showProgress();
            cropBtn.disabled = true;
            expandBtn.disabled = true;

            try {
                await core.executeAsModal(async () => {
                    const doc = app.activeDocument;

                    // 步骤 1: 检查选区和原图层
                    showStatus('正在分析选区...', '');
                    updateProgress(10);

                    // 检查是否有选区
                    const hasSelection = await checkActiveSelection();
                    if (!hasSelection) {
                        throw new Error('请先使用选择工具建立选区');
                    }

                    // 获取原图层信息
                    const originalLayer = doc.activeLayer;
                    const originalLayerName = originalLayer.name;

                    // 步骤 2: 智能边缘优化（参考Live2D的ConvNeXt边缘检测）
                    showStatus('正在优化选区边缘...', '');
                    updateProgress(20);

                    // 优化选区边缘以获得更精确的对象轮廓
                    try {
                        await app.batchPlay([{
                            "_obj": "refineSelectionEdge",
                            "radius": {
                                "_unit": "pixelsUnit",
                                "_value": 1.5
                            },
                            "smooth": {
                                "_unit": "percentUnit",
                                "_value": 5
                            },
                            "feather": {
                                "_unit": "pixelsUnit",
                                "_value": 0.3
                            },
                            "contrast": {
                                "_unit": "percentUnit",
                                "_value": 15
                            }
                        }], {});
                    } catch (e) {
                        // 如果智能边缘失败，使用基本羽化
                        await app.batchPlay([{
                            "_obj": "feather",
                            "radius": {
                                "_unit": "pixelsUnit",
                                "_value": 0.5
                            }
                        }], {});
                    }

                    // 步骤 3: 创建裁剪对象图层
                    showStatus('正在创建裁剪对象...', '');
                    updateProgress(35);

                    // 复制选区内容到新图层
                    await app.batchPlay([{
                        "_obj": "copyToLayer"
                    }], {});

                    const croppedLayer = doc.activeLayer;
                    croppedLayer.name = originalLayerName + ' 裁剪';

                    // 步骤 4: 创建组织结构
                    let targetGroup = null;
                    if (settings.createGroup) {
                        targetGroup = await doc.layerGroups.add();
                        targetGroup.name = 'Live2D 材质分离 - ' + new Date().toLocaleTimeString();
                        await croppedLayer.move(targetGroup, "inside");
                    }

                    // 步骤 5: 魔术般的背景修复（基于Live2D的LaMa大型掩码修复原理）
                    if (settings.fillCropArea) {
                        showStatus('正在魔术般修复原图背景...', '');
                        updateProgress(50);

                        // 选择原始图层进行修复
                        await originalLayer.duplicate();
                        const repairedLayer = doc.activeLayer;
                        repairedLayer.name = originalLayerName; // 保持原图层名称

                        // 将修复后的图层移到组中（如果有组）
                        if (targetGroup) {
                            await repairedLayer.move(targetGroup, "inside");
                        }

                        // 重新选择相同的区域进行修复
                        await app.batchPlay([{
                            "_obj": "reselect"
                        }], {});

                        // 扩展选区以获得更好的修复上下文（模拟LaMa的大型掩码）
                        try {
                            await app.batchPlay([{
                                "_obj": "expand",
                                "by": {
                                    "_unit": "pixelsUnit",
                                    "_value": 3
                                }
                            }], {});
                        } catch (e) {
                            console.log('选区扩展跳过');
                        }

                        // 使用内容感知填充进行智能修复（模拟LaMa的修复算法）
                        showStatus('正在进行智能像素修复...', '');
                        updateProgress(65);

                        try {
                            // 方法1: 使用内容感知填充
                            await app.batchPlay([{
                                "_obj": "fill",
                                "using": {
                                    "_enum": "fillContents",
                                    "_value": "contentAware"
                                },
                                "opacity": {
                                    "_unit": "percentUnit",
                                    "_value": 100
                                },
                                "mode": {
                                    "_enum": "blendMode",
                                    "_value": "normal"
                                }
                            }], {});
                        } catch (e) {
                            // 方法2: 使用修复画笔的自动化版本
                            showStatus('使用高级修复算法...', '');
                            updateProgress(70);

                            try {
                                // 使用修复工具
                                await app.batchPlay([{
                                    "_obj": "heal",
                                    "source": {
                                        "_enum": "healingSource",
                                        "_value": "contentAware"
                                    }
                                }], {});
                            } catch (e2) {
                                // 方法3: 使用仿制图章的智能版本
                                await app.batchPlay([{
                                    "_obj": "fill",
                                    "using": {
                                        "_enum": "fillContents",
                                        "_value": "pattern"
                                    },
                                    "pattern": {
                                        "_enum": "pattern",
                                        "_value": "contentAware"
                                    }
                                }], {});
                            }
                        }

                        // 进行后处理优化
                        showStatus('正在优化修复效果...', '');
                        updateProgress(75);

                        // 轻微模糊以融合修复区域
                        try {
                            await app.batchPlay([{
                                "_obj": "gaussianBlur",
                                "radius": {
                                    "_unit": "pixelsUnit",
                                    "_value": 0.3
                                }
                            }], {});
                        } catch (e) {
                            console.log('后处理模糊跳过');
                        }
                    }

                    // 步骤 6: 扩展裁剪区域（如果需要）
                    if (settings.expandCropArea) {
                        showStatus('正在扩展裁剪区域...', '');
                        updateProgress(80);

                        // 选择裁剪图层并扩展其内容
                        await croppedLayer.duplicate();
                        const expandedCropLayer = doc.activeLayer;
                        expandedCropLayer.name = originalLayerName + ' 裁剪 (扩展)';

                        // 扩展图层内容
                        try {
                            await app.batchPlay([{
                                "_obj": "transform",
                                "freeTransformCenterState": {
                                    "_enum": "quadCenterState",
                                    "_value": "QCSAverage"
                                },
                                "scale": {
                                    "_unit": "percentUnit",
                                    "_value": 105
                                }
                            }], {});
                        } catch (e) {
                            console.log('扩展变换跳过');
                        }

                        if (targetGroup) {
                            await expandedCropLayer.move(targetGroup, "inside");
                        }
                    }

                    // 步骤 7: 最终整理和优化
                    showStatus('正在整理图层结构...', '');
                    updateProgress(90);

                    // 确保图层顺序正确：裁剪图层在上，修复图层在下
                    if (targetGroup) {
                        // 在组内排列图层顺序
                        const groupLayers = targetGroup.layers;
                        for (let i = 0; i < groupLayers.length; i++) {
                            const layer = groupLayers[i];
                            if (layer.name.includes('裁剪')) {
                                // 裁剪图层移到最上方
                                await layer.move(targetGroup, "inside");
                            }
                        }
                    }

                    // 清除选区
                    await app.batchPlay([{
                        "_obj": "deselect"
                    }], {});

                    // 隐藏原始图层（如果在组外）
                    if (!targetGroup) {
                        try {
                            originalLayer.visible = false;
                        } catch (e) {
                            console.log('原始图层隐藏跳过');
                        }
                    }
                });

                // 完成
                updateProgress(100);
                showStatus('🎉 魔术般的材质分离完成！', 'success');

                setTimeout(() => {
                    hideProgress();
                    showStatus('准备下次处理 - 请先建立选区', '');
                }, 3000);

            } catch (error) {
                throw error;
            } finally {
                cropBtn.disabled = false;
                expandBtn.disabled = false;
            }
        }

        // 扩展处理函数 - 基于Live2D-MaterialSeparation的DeepLabv3+LaMa原理
        async function processExpand(settings) {
            showProgress();
            cropBtn.disabled = true;
            expandBtn.disabled = true;

            try {
                // 步骤 1: 分析选区和透明区域
                showStatus('正在分析透明区域...', '');
                updateProgress(15);

                await core.executeAsModal(async () => {
                    const doc = app.activeDocument;

                    // 检查是否有选区
                    const hasSelection = await checkActiveSelection();
                    if (!hasSelection) {
                        throw new Error('请先使用选择工具建立选区');
                    }

                    // 步骤 2: 智能边缘分析（模拟DeepLabv3语义分割）
                    showStatus('正在进行边缘分析...', '');
                    updateProgress(30);

                    // 创建组织结构
                    let targetGroup = null;
                    if (settings.createGroup) {
                        targetGroup = await doc.layerGroups.add();
                        targetGroup.name = 'Live2D 扩展结果 - ' + new Date().toLocaleTimeString();
                    }

                    // 复制当前图层作为扩展基础
                    const activeLayer = doc.activeLayer;
                    await activeLayer.duplicate();
                    const expandLayer = doc.activeLayer;
                    expandLayer.name = '扩展图层';

                    if (targetGroup) {
                        await expandLayer.move(targetGroup, "inside");
                    }

                    // 步骤 3: 智能像素扩展（基于LaMa修复原理）
                    showStatus('正在智能扩展像素...', '');
                    updateProgress(50);

                    // 方法1: 使用内容感知填充（模拟LaMa的大型掩码修复）
                    try {
                        await app.batchPlay([{
                            "_obj": "fill",
                            "using": {
                                "_enum": "fillContents",
                                "_value": "contentAware"
                            },
                            "opacity": {
                                "_unit": "percentUnit",
                                "_value": 100
                            },
                            "mode": {
                                "_enum": "blendMode",
                                "_value": "normal"
                            }
                        }], {});
                    } catch (e) {
                        // 方法2: 使用修复画笔工具的自动化版本
                        showStatus('使用备用扩展方法...', '');
                        updateProgress(65);

                        // 扩展选区以包含更多上下文
                        await app.batchPlay([{
                            "_obj": "expand",
                            "by": {
                                "_unit": "pixelsUnit",
                                "_value": 10
                            }
                        }], {});

                        // 使用修复填充
                        await app.batchPlay([{
                            "_obj": "fill",
                            "using": {
                                "_enum": "fillContents",
                                "_value": "pattern"
                            },
                            "pattern": {
                                "_enum": "pattern",
                                "_value": "contentAware"
                            }
                        }], {});
                    }

                    // 步骤 4: 边缘优化和平滑（模拟ConvNeXt特征提取的后处理）
                    showStatus('正在优化边缘...', '');
                    updateProgress(75);

                    // 应用轻微的高斯模糊来平滑边缘
                    await app.batchPlay([{
                        "_obj": "gaussianBlur",
                        "radius": {
                            "_unit": "pixelsUnit",
                            "_value": 0.5
                        }
                    }], {});

                    // 步骤 5: 色彩匹配和融合
                    showStatus('正在进行色彩融合...', '');
                    updateProgress(85);

                    // 使用自动色调匹配
                    try {
                        await app.batchPlay([{
                            "_obj": "matchColor",
                            "source": {
                                "_ref": "layer",
                                "_enum": "ordinal",
                                "_value": "targetEnum"
                            },
                            "luminance": true,
                            "colorIntensity": {
                                "_unit": "percentUnit",
                                "_value": 100
                            },
                            "fade": {
                                "_unit": "percentUnit",
                                "_value": 0
                            }
                        }], {});
                    } catch (e) {
                        console.log('色彩匹配跳过');
                    }

                    // 步骤 6: 最终优化
                    if (settings.useLayerMask) {
                        // 创建图层蒙版以实现更好的融合
                        await app.batchPlay([{
                            "_obj": "make",
                            "new": {
                                "_enum": "channel",
                                "_value": "mask"
                            },
                            "at": {
                                "_ref": "channel",
                                "_enum": "channel",
                                "_value": "mask"
                            },
                            "using": {
                                "_enum": "userMaskEnabled",
                                "_value": "revealSelection"
                            }
                        }], {});
                    }

                    // 清除选区
                    await app.batchPlay([{
                        "_obj": "deselect"
                    }], {});
                });

                // 完成
                updateProgress(100);
                showStatus('智能扩展完成！', 'success');

                setTimeout(() => {
                    hideProgress();
                    showStatus('准备下次处理', '');
                }, 2000);

            } catch (error) {
                throw error;
            } finally {
                cropBtn.disabled = false;
                expandBtn.disabled = false;
            }
        }

        // UI 辅助函数
        function showProgress() {
            progress.style.display = 'block';
        }

        function hideProgress() {
            progress.style.display = 'none';
            updateProgress(0);
        }

        function updateProgress(percent) {
            progressBar.style.width = percent + '%';
        }

        function showStatus(message, type = '') {
            status.textContent = message;
            status.className = 'status ' + type;
        }

        // 错误处理包装器
        function safeExecute(fn) {
            return async (...args) => {
                try {
                    return await fn(...args);
                } catch (error) {
                    console.error('执行错误:', error);
                    showStatus('操作失败: ' + (error.message || 'undefined'), 'error');
                    hideProgress();
                    cropBtn.disabled = false;
                    expandBtn.disabled = false;
                    throw error;
                }
            };
        }

        // 包装主要函数
        const safeCrop = safeExecute(processCrop);
        const safeExpand = safeExecute(processExpand);

        // 重新绑定事件处理器
        cropBtn.addEventListener('click', async () => {
            try {
                if (!app.activeDocument) {
                    showStatus('请先打开一个文档', 'error');
                    return;
                }

                const hasSelection = await checkSelection();
                if (!hasSelection) {
                    showStatus('请先使用选择工具建立选区', 'error');
                    return;
                }

                const settings = getSettings();
                await safeCrop(settings);

            } catch (error) {
                console.error('裁剪按钮错误:', error);
            }
        });

        expandBtn.addEventListener('click', async () => {
            try {
                if (!app.activeDocument) {
                    showStatus('请先打开一个文档', 'error');
                    return;
                }

                const hasSelection = await checkSelection();
                if (!hasSelection) {
                    showStatus('请先使用选择工具建立选区', 'error');
                    return;
                }

                const settings = getSettings();
                await safeExpand(settings);

            } catch (error) {
                console.error('扩展按钮错误:', error);
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            showStatus('🎯 准备处理 - 请先建立选区', '');

            // 验证UI元素
            const elements = ['createGroup', 'useLayerMask', 'fillCropArea', 'expandCropArea'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    console.error('缺失UI元素:', id);
                } else {
                    console.log('✅ UI元素正常:', id);
                }
            });
        });

        console.log('🎉 Live2D 材质分离工具加载成功！');
        console.log('✅ 开源版本 - 无需许可证验证');
        console.log('🔪 裁剪功能：抠图并自动补全背景');
        console.log('📈 扩展功能：扩展透明区域像素');
    </script>
</body>
</html>
