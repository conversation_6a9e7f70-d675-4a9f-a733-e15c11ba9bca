<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Spine2D Debug</title>
    <style>
        body {
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--uxp-host-background-color);
            color: var(--uxp-host-text-color);
        }

        .button {
            width: 100%;
            padding: 12px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 8px;
        }

        .status {
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
            min-height: 16px;
            margin-top: 10px;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .error {
            color: #cc0000;
        }

        .success {
            color: #00aa00;
        }
    </style>
</head>
<body>
    <h2>Spine2D 调试版本</h2>

    <button class="button" id="testBtn">🔍 测试基本功能</button>
    <button class="button" id="cropBtn">🔪 测试裁剪</button>

    <div class="status" id="status">准备测试...</div>

    <script>
        const { app, core } = require('photoshop');

        const testBtn = document.getElementById('testBtn');
        const cropBtn = document.getElementById('cropBtn');
        const status = document.getElementById('status');

        function showStatus(message, type = '') {
            status.textContent = message;
            status.className = 'status ' + type;
            console.log('状态:', message);
        }

        // 测试基本功能
        testBtn.addEventListener('click', async () => {
            try {
                showStatus('测试开始...');

                // 测试1: 检查Photoshop连接
                if (!app) {
                    showStatus('错误: 无法连接到Photoshop app对象', 'error');
                    return;
                }
                showStatus('✅ Photoshop app对象正常');

                // 测试2: 检查文档
                if (!app.activeDocument) {
                    showStatus('错误: 没有活动文档，请先打开一个图像', 'error');
                    return;
                }
                showStatus('✅ 活动文档存在: ' + app.activeDocument.name);

                // 测试3: 检查core
                if (!core) {
                    showStatus('错误: 无法访问core对象', 'error');
                    return;
                }
                showStatus('✅ core对象正常');

                // 测试4: 检查选区
                try {
                    await core.executeAsModal(async () => {
                        const doc = app.activeDocument;
                        const bounds = doc.selection.bounds;
                        showStatus('✅ 检测到选区: ' + JSON.stringify(bounds), 'success');
                    });
                } catch (e) {
                    showStatus('⚠️ 没有检测到选区，请先建立选区', 'error');
                }

            } catch (error) {
                console.error('测试错误:', error);
                showStatus('测试失败: ' + error.message, 'error');
            }
        });

        // 测试真正的裁剪功能
        cropBtn.addEventListener('click', async () => {
            try {
                showStatus('开始真正的裁剪测试...');

                if (!app.activeDocument) {
                    showStatus('请先打开一个文档', 'error');
                    return;
                }

                await core.executeAsModal(async () => {
                    const doc = app.activeDocument;

                    // 检查选区
                    try {
                        const bounds = doc.selection.bounds;
                        showStatus('检测到选区，开始魔术裁剪...');
                    } catch (e) {
                        throw new Error('请先建立选区');
                    }

                    // 获取原图层信息
                    const originalLayer = doc.activeLayer;
                    const originalLayerName = originalLayer.name;

                    // 步骤1: 复制选区到新图层（裁剪对象）
                    showStatus('正在创建裁剪对象...');

                    await app.batchPlay([{
                        "_obj": "copyToLayer"
                    }], {});

                    const croppedLayer = doc.activeLayer;
                    croppedLayer.name = originalLayerName + ' 裁剪';

                    // 步骤2: 创建修复后的原图层
                    showStatus('正在修复原图背景...');

                    // 选择原图层
                    await originalLayer.duplicate();
                    const repairedLayer = doc.activeLayer;
                    repairedLayer.name = originalLayerName + ' 修复';

                    // 重新选择相同区域
                    await app.batchPlay([{
                        "_obj": "reselect"
                    }], {});

                    // 使用内容感知填充修复被裁剪的区域
                    try {
                        await app.batchPlay([{
                            "_obj": "fill",
                            "using": {
                                "_enum": "fillContents",
                                "_value": "contentAware"
                            },
                            "opacity": {
                                "_unit": "percentUnit",
                                "_value": 100
                            },
                            "mode": {
                                "_enum": "blendMode",
                                "_value": "normal"
                            }
                        }], {});

                        showStatus('✅ 魔术裁剪成功！对象已分离，背景已修复', 'success');

                    } catch (fillError) {
                        // 如果内容感知填充失败，使用删除方式
                        await app.batchPlay([{
                            "_obj": "delete"
                        }], {});

                        showStatus('✅ 裁剪成功！对象已分离（背景已删除）', 'success');
                    }

                    // 清除选区
                    await app.batchPlay([{
                        "_obj": "deselect"
                    }], {});

                    // 隐藏原始图层
                    originalLayer.visible = false;
                });

            } catch (error) {
                console.error('裁剪测试错误:', error);
                showStatus('裁剪测试失败: ' + (error.message || '未知错误'), 'error');
            }
        });

        // 初始化
        showStatus('调试版本已加载，点击测试按钮开始');
        console.log('🔍 Spine2D 调试版本已加载');

    </script>
</body>
</html>
