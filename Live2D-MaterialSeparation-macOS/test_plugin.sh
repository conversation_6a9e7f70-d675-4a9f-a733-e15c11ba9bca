#!/bin/bash

# Live2D Material Separation Plugin for macOS
# Test Script
# Open Source Version - No License Verification

# Set error handling
set -e

# Define colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SOURCE_DIR="$SCRIPT_DIR/Source"
MODELS_DIR="$SCRIPT_DIR/Models"
TEST_DIR="$SCRIPT_DIR/Tests"

# Print banner
echo -e "${BLUE}==================================================${NC}"
echo -e "${BLUE}  Live2D Material Separation Plugin Tester${NC}"
echo -e "${BLUE}  Open Source Version for macOS${NC}"
echo -e "${BLUE}==================================================${NC}"
echo ""

# Function to run a test
run_test() {
    local test_name=$1
    local test_command=$2
    
    echo -e "${YELLOW}Running test: $test_name${NC}"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✓ $test_name passed${NC}"
        return 0
    else
        echo -e "${RED}✗ $test_name failed${NC}"
        return 1
    fi
}

# Test 1: Check Python installation
echo -e "${YELLOW}Test 1: Checking Python installation...${NC}"
if command -v python3 >/dev/null 2>&1; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -ge 3 ] && [ "$PYTHON_MINOR" -ge 10 ]; then
        echo -e "${GREEN}✓ Python $PYTHON_VERSION detected${NC}"
    else
        echo -e "${RED}✗ Python 3.10+ required, but $PYTHON_VERSION found${NC}"
        exit 1
    fi
else
    echo -e "${RED}✗ Python 3 not found${NC}"
    exit 1
fi

# Test 2: Check project structure
echo -e "\n${YELLOW}Test 2: Checking project structure...${NC}"
required_files=(
    "Source/Core/MaterialSeparationCore.mm"
    "Source/Plugin/index.html"
    "Source/Plugin/manifest.xml"
    "Source/Python/material_separation.py"
    "requirements.txt"
    "build.sh"
    "install_plugin.sh"
    "download_models.sh"
)

for file in "${required_files[@]}"; do
    if [ -f "$SCRIPT_DIR/$file" ]; then
        echo -e "${GREEN}✓ Found $file${NC}"
    else
        echo -e "${RED}✗ Missing $file${NC}"
        exit 1
    fi
done

# Test 3: Check if virtual environment can be created
echo -e "\n${YELLOW}Test 3: Testing virtual environment creation...${NC}"
TEST_VENV="$SCRIPT_DIR/test_venv"
if [ -d "$TEST_VENV" ]; then
    rm -rf "$TEST_VENV"
fi

python3 -m venv "$TEST_VENV"
if [ -d "$TEST_VENV" ]; then
    echo -e "${GREEN}✓ Virtual environment created successfully${NC}"
    rm -rf "$TEST_VENV"
else
    echo -e "${RED}✗ Failed to create virtual environment${NC}"
    exit 1
fi

# Test 4: Test Python module syntax
echo -e "\n${YELLOW}Test 4: Testing Python module syntax...${NC}"
python3 -m py_compile "$SOURCE_DIR/Python/material_separation.py"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Python module syntax is valid${NC}"
else
    echo -e "${RED}✗ Python module has syntax errors${NC}"
    exit 1
fi

# Test 5: Test Python module import
echo -e "\n${YELLOW}Test 5: Testing Python module import...${NC}"
cd "$SOURCE_DIR/Python"
python3 -c "import material_separation; print('Module imported successfully')"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Python module can be imported${NC}"
else
    echo -e "${RED}✗ Python module import failed${NC}"
    exit 1
fi
cd "$SCRIPT_DIR"

# Test 6: Test build script syntax
echo -e "\n${YELLOW}Test 6: Testing build script syntax...${NC}"
bash -n "$SCRIPT_DIR/build.sh"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Build script syntax is valid${NC}"
else
    echo -e "${RED}✗ Build script has syntax errors${NC}"
    exit 1
fi

# Test 7: Test install script syntax
echo -e "\n${YELLOW}Test 7: Testing install script syntax...${NC}"
bash -n "$SCRIPT_DIR/install_plugin.sh"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Install script syntax is valid${NC}"
else
    echo -e "${RED}✗ Install script has syntax errors${NC}"
    exit 1
fi

# Test 8: Test model download script syntax
echo -e "\n${YELLOW}Test 8: Testing model download script syntax...${NC}"
bash -n "$SCRIPT_DIR/download_models.sh"
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Model download script syntax is valid${NC}"
else
    echo -e "${RED}✗ Model download script has syntax errors${NC}"
    exit 1
fi

# Test 9: Check Xcode tools (required for building)
echo -e "\n${YELLOW}Test 9: Checking Xcode command line tools...${NC}"
if xcode-select -p &> /dev/null; then
    echo -e "${GREEN}✓ Xcode command line tools are installed${NC}"
else
    echo -e "${YELLOW}⚠ Xcode command line tools not found${NC}"
    echo -e "${YELLOW}  Install with: xcode-select --install${NC}"
fi

# Test 10: Check for Photoshop installation
echo -e "\n${YELLOW}Test 10: Checking for Adobe Photoshop...${NC}"
ps_found=false
for version in 2024 2023 2022 2021; do
    if [ -d "/Applications/Adobe Photoshop $version" ]; then
        echo -e "${GREEN}✓ Adobe Photoshop $version found${NC}"
        ps_found=true
        break
    fi
done

if [ "$ps_found" = false ]; then
    echo -e "${YELLOW}⚠ Adobe Photoshop not found${NC}"
    echo -e "${YELLOW}  Plugin requires Photoshop 2021 or newer${NC}"
fi

# Create test report
echo -e "\n${YELLOW}Creating test report...${NC}"
mkdir -p "$TEST_DIR"
cat > "$TEST_DIR/test_report.md" << EOF
# Live2D Material Separation Plugin Test Report

Generated on: $(date)

## Test Results

### Environment
- Python Version: $PYTHON_VERSION
- macOS Version: $(sw_vers -productVersion)
- Xcode Tools: $(xcode-select -p 2>/dev/null || echo "Not installed")

### Test Summary
- ✅ Python installation check
- ✅ Project structure validation
- ✅ Virtual environment creation
- ✅ Python module syntax validation
- ✅ Python module import test
- ✅ Build script syntax check
- ✅ Install script syntax check
- ✅ Model download script syntax check
- $(if xcode-select -p &> /dev/null; then echo "✅"; else echo "⚠️"; fi) Xcode command line tools
- $(if [ "$ps_found" = true ]; then echo "✅"; else echo "⚠️"; fi) Adobe Photoshop installation

### Recommendations

$(if ! xcode-select -p &> /dev/null; then echo "- Install Xcode command line tools: \`xcode-select --install\`"; fi)
$(if [ "$ps_found" = false ]; then echo "- Install Adobe Photoshop 2021 or newer for plugin testing"; fi)
- Run \`./download_models.sh\` to download AI models
- Run \`./build.sh\` to build the plugin
- Run \`./install_plugin.sh\` to install the plugin

### Next Steps

1. Download required AI models
2. Build the plugin
3. Install and test in Photoshop
4. Verify functionality with test images

EOF

echo -e "${GREEN}✓ Test report created at $TEST_DIR/test_report.md${NC}"

# Final summary
echo -e "\n${BLUE}==================================================${NC}"
echo -e "${BLUE}  Test Summary${NC}"
echo -e "${BLUE}==================================================${NC}"
echo -e "\n${GREEN}All core tests passed!${NC}"
echo -e "\nYour Live2D Material Separation Plugin is ready for:"
echo -e "1. Model download: ${YELLOW}./download_models.sh${NC}"
echo -e "2. Building: ${YELLOW}./build.sh${NC}"
echo -e "3. Installation: ${YELLOW}./install_plugin.sh${NC}"
echo -e "\nTest report saved to: ${YELLOW}$TEST_DIR/test_report.md${NC}\n"

exit 0
