#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Live2D Material Separation Plugin - Status Checker
Quick status check for deployment verification
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header(title):
    print(f"\n{'='*50}")
    print(f"  {title}")
    print(f"{'='*50}")

def print_check(description, status, details=""):
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {description}")
    if details:
        print(f"   {details}")

def check_python():
    """Check Python installation"""
    print_header("Python Environment Check")
    
    # Check Python version
    try:
        version = sys.version.split()[0]
        major, minor = map(int, version.split('.')[:2])
        python_ok = major >= 3 and minor >= 10
        print_check(f"Python Version: {version}", python_ok, 
                   "Requires Python 3.10+" if not python_ok else "")
    except:
        print_check("Python Version", False, "Could not determine Python version")
        return False
    
    # Check virtual environment
    venv_path = Path("venv")
    venv_exists = venv_path.exists() and venv_path.is_dir()
    print_check("Virtual Environment", venv_exists, 
               str(venv_path.absolute()) if venv_exists else "Run: python3 -m venv venv")
    
    return python_ok and venv_exists

def check_dependencies():
    """Check Python dependencies"""
    print_header("Dependencies Check")
    
    required_packages = ['numpy', 'torch', 'PIL', 'logging']
    all_ok = True
    
    for package in required_packages:
        try:
            __import__(package)
            print_check(f"Package: {package}", True)
        except ImportError:
            print_check(f"Package: {package}", False, f"Install with: pip install {package}")
            all_ok = False
    
    return all_ok

def check_project_structure():
    """Check project file structure"""
    print_header("Project Structure Check")
    
    required_files = [
        "Source/Core/MaterialSeparationCore.mm",
        "Source/Plugin/index.html",
        "Source/Plugin/manifest.xml",
        "Source/Python/material_separation.py",
        "requirements.txt",
        "build.sh",
        "install_plugin.sh"
    ]
    
    all_ok = True
    for file_path in required_files:
        path = Path(file_path)
        exists = path.exists()
        print_check(f"File: {file_path}", exists)
        if not exists:
            all_ok = False
    
    return all_ok

def check_photoshop():
    """Check Photoshop installation"""
    print_header("Adobe Photoshop Check")
    
    ps_versions = ["2024", "2023", "2022", "2021"]
    ps_found = False
    
    for version in ps_versions:
        ps_path = Path(f"/Applications/Adobe Photoshop {version}")
        if ps_path.exists():
            print_check(f"Photoshop {version}", True, str(ps_path))
            ps_found = True
            break
    
    if not ps_found:
        print_check("Adobe Photoshop", False, "Install Photoshop 2021 or newer")
    
    return ps_found

def check_plugin_installation():
    """Check if plugin is installed"""
    print_header("Plugin Installation Check")
    
    cep_dir = Path.home() / "Library/Application Support/Adobe/CEP/extensions"
    plugin_dir = cep_dir / "com.live2d.materialseparation"
    
    print_check("CEP Extensions Directory", cep_dir.exists(), str(cep_dir))
    print_check("Plugin Directory", plugin_dir.exists(), str(plugin_dir))
    
    if plugin_dir.exists():
        key_files = ["index.html", "manifest.xml"]
        for file_name in key_files:
            file_path = plugin_dir / file_name
            print_check(f"Plugin File: {file_name}", file_path.exists())
    
    return plugin_dir.exists()

def check_xcode_tools():
    """Check Xcode command line tools"""
    print_header("Development Tools Check")
    
    try:
        result = subprocess.run(['xcode-select', '-p'], 
                              capture_output=True, text=True, check=True)
        xcode_path = result.stdout.strip()
        print_check("Xcode Command Line Tools", True, xcode_path)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_check("Xcode Command Line Tools", False, "Install with: xcode-select --install")
        return False

def main():
    """Main status check function"""
    print("🔍 Live2D Material Separation Plugin - Status Check")
    print(f"📍 Working Directory: {Path.cwd()}")
    
    # Run all checks
    checks = [
        ("Python Environment", check_python),
        ("Dependencies", check_dependencies),
        ("Project Structure", check_project_structure),
        ("Development Tools", check_xcode_tools),
        ("Adobe Photoshop", check_photoshop),
        ("Plugin Installation", check_plugin_installation)
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print_check(f"Error in {name}", False, str(e))
            results[name] = False
    
    # Summary
    print_header("Summary")
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"📊 Checks Passed: {passed_checks}/{total_checks}")
    
    if passed_checks == total_checks:
        print("🎉 All checks passed! Your plugin is ready to use.")
    elif passed_checks >= total_checks - 2:
        print("⚠️  Most checks passed. Minor issues may need attention.")
    else:
        print("❌ Several issues found. Please review the failed checks above.")
    
    # Next steps
    print_header("Next Steps")
    if not results.get("Python Environment", False):
        print("1. Create virtual environment: python3 -m venv venv")
        print("2. Activate environment: source venv/bin/activate")
    
    if not results.get("Dependencies", False):
        print("3. Install dependencies: pip install -r requirements.txt")
    
    if not results.get("Plugin Installation", False):
        print("4. Install plugin: ./install_plugin.sh")
    
    if results.get("Adobe Photoshop", False) and results.get("Plugin Installation", False):
        print("5. Restart Photoshop and look for the plugin in Window > Extensions")
    
    print("\n📚 For detailed instructions, see MANUAL_DEPLOY.md")

if __name__ == "__main__":
    main()
