# 🔍 故障排除指南

## 🚨 问题：点击裁剪按钮没有任何反应

我已经创建了一个调试版本来帮助诊断问题。

### 🔧 安装调试版本

调试版本已经安装，现在请按照以下步骤进行诊断：

### 📋 诊断步骤

#### 步骤1: 重启Photoshop
```
完全退出Photoshop (Cmd+Q)
重新启动Photoshop
```

#### 步骤2: 打开调试插件
```
窗口 > 扩展功能 > Live2D 材质分离工具
```

您应该看到：
```
Live2D 调试版本
🔍 测试基本功能
🔪 测试裁剪
准备测试...
```

#### 步骤3: 运行基本测试
1. **打开一个图像文件**
2. **点击"🔍 测试基本功能"按钮**
3. **查看状态信息**

**预期结果**：
```
✅ Photoshop app对象正常
✅ 活动文档存在: [文档名]
✅ core对象正常
⚠️ 没有检测到选区，请先建立选区
```

#### 步骤4: 测试选区检测
1. **使用选择工具建立选区**（矩形选框、套索等）
2. **再次点击"🔍 测试基本功能"按钮**

**预期结果**：
```
✅ Photoshop app对象正常
✅ 活动文档存在: [文档名]
✅ core对象正常
✅ 检测到选区: {"left":100,"top":50,"right":200,"bottom":150}
```

#### 步骤5: 测试裁剪功能
1. **确保有选区**
2. **点击"🔪 测试裁剪"按钮**

**预期结果**：
```
检测到选区，开始处理...
正在复制选区...
✅ 裁剪测试成功！创建了新图层
```

### 🔍 可能的问题和解决方案

#### 问题A: 插件不显示
**症状**: 在扩展功能菜单中找不到插件

**解决方案**:
```bash
# 检查插件是否正确安装
ls -la "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation/"

# 应该看到：
# debug.html
# manifest.json
# icons/
```

**如果文件缺失**:
```bash
sudo rm -rf "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
sudo cp -R "/Volumes/JOJOCsy/Ai探索/Cursor/拆图/Live2D-MaterialSeparation-macOS/UXP-Plugin" "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
sudo chmod -R 755 "/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"
```

#### 问题B: 测试基本功能失败
**症状**: 点击测试按钮显示错误

**可能原因和解决方案**:

1. **"无法连接到Photoshop app对象"**
   - UXP API未正确加载
   - 重启Photoshop
   - 检查Photoshop版本是否支持UXP

2. **"没有活动文档"**
   - 在Photoshop中打开一个图像文件
   - 确保文档是活动状态

3. **"无法访问core对象"**
   - Photoshop版本过旧
   - 需要Photoshop 2021或更新版本

#### 问题C: 选区检测失败
**症状**: 显示"没有检测到选区"

**解决方案**:
1. **确保正确建立选区**:
   - 使用矩形选框工具
   - 在图像上拖拽建立选区
   - 确保看到"行进蚁线"

2. **检查选区类型**:
   - 避免使用路径或形状
   - 使用标准的像素选区

#### 问题D: 裁剪测试失败
**症状**: 显示"裁剪测试失败"

**可能原因**:
1. **权限问题** - Photoshop没有修改文档的权限
2. **图层锁定** - 当前图层被锁定
3. **文档模式** - 文档处于特殊模式

**解决方案**:
1. **解锁图层** - 确保当前图层没有锁定
2. **检查文档模式** - 确保不在快速蒙版模式
3. **重新创建文档** - 新建一个简单的文档测试

### 🔧 高级诊断

#### 检查浏览器控制台
1. **在插件面板中按F12**（如果支持）
2. **查看Console标签页**
3. **寻找JavaScript错误**

#### 检查Photoshop版本
```
帮助 > 关于Photoshop
确保版本为2021或更新
```

#### 检查UXP支持
```
窗口 > 扩展功能
如果菜单为空，说明UXP未启用
```

### 📞 报告问题

如果调试版本仍然不工作，请提供以下信息：

1. **Photoshop版本**: 帮助 > 关于Photoshop
2. **macOS版本**: 关于本机
3. **测试结果**: 每个测试按钮的具体输出
4. **错误信息**: 任何显示的错误消息
5. **控制台日志**: 浏览器控制台中的错误（如果可访问）

### 🎯 下一步

根据调试结果，我们可以：

1. **如果基本测试通过** - 问题在复杂的处理逻辑中
2. **如果选区检测失败** - 问题在选区API调用中
3. **如果裁剪测试失败** - 问题在batchPlay API中
4. **如果插件不显示** - 问题在安装或配置中

请运行调试版本并告诉我具体的测试结果，这样我就能准确定位问题并提供解决方案！
