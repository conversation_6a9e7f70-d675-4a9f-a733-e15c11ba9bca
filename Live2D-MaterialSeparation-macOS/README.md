# Live2D Material Separation Plugin for macOS

## 概述

这是Live2D Cubism Material Separation插件的macOS版本，一个强大的Photoshop扩展插件，可以自动将Live2D角色图像分离为不同的材质层。该版本完全开源，移除了许可证验证、在线验证和硬件指纹绑定机制。

## 主要特点

- 自动识别和分离Live2D角色图像中的不同材质（皮肤、头发、眼睛、衣服等）
- 生成分层PSD文件，便于Live2D模型制作
- 支持质量和细节级别的调整
- 完全开源，无许可证验证
- 为Apple Silicon优化，使用Metal性能加速

## 系统要求

- macOS 11.0 (Big Sur) 或更高版本
- Adobe Photoshop 2021 (22.0) 或更高版本
- Python 3.10 或更高版本

## 安装方法

### 自动安装

1. 确保已安装Python 3.10或更高版本
2. 打开终端，进入项目目录
3. 运行安装脚本：`./install_plugin.sh`
4. 重启Photoshop

### 手动安装

1. 确保已安装Python 3.10或更高版本
2. 运行`./download_models.sh`下载必要的模型文件
3. 运行`./build.sh`构建项目
4. 将生成的插件文件复制到Adobe CEP扩展目录：
   `/Library/Application Support/Adobe/CEP/extensions/`
5. 重启Photoshop

## 使用方法

1. 在Photoshop中，选择`窗口 > 扩展功能 > Live2D Material Separation`
2. 选择要处理的图像（可以是当前文档或从文件选择）
3. 调整质量和细节级别
4. 选择要提取的材质类型
5. 点击"处理图像"按钮
6. 等待处理完成，插件会自动生成分层PSD文件

## 开发指南

### 项目结构

- `Source/Plugin`: Photoshop CEP扩展文件
- `Source/Core`: 核心处理库（Objective-C++）
- `Source/Python`: Python模块和深度学习模型接口
- `Models`: 预训练的深度学习模型
- `Build`: 构建输出目录

### 构建项目

1. 安装必要的依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 使用Makefile构建核心库：
   ```bash
   cd Source/Core
   make
   ```

3. 或使用提供的构建脚本：
   ```bash
   ./build.sh
   ```

## 故障排除

- 如果插件在Photoshop中不可见，检查CEP扩展目录权限
- 如果处理失败，检查Python环境和依赖安装
- 查看日志文件：`~/Library/Logs/CSXS/CEP*.log`

## 许可证

本项目采用MIT许可证，详见[LICENSE](LICENSE)文件。

## 致谢

- 本项目使用了多个开源库和工具，包括PyTorch、OpenCV等
- 感谢社区为原始Windows版本提供的宝贵开发经验

## 技术架构

### 深度学习模型

本插件使用以下AI模型进行材质分离：

1. **ConvNeXt**: 用于图像特征提取
2. **DeepLabv3**: 用于语义分割
3. **LaMa**: 用于图像修复和填充

### 性能优化

- 使用Metal Performance Shaders (MPS)进行GPU加速
- 支持Apple Silicon原生性能
- 多线程处理提高效率

## 开发状态

### ✅ 已完成
- 核心架构设计
- Photoshop插件界面
- Python深度学习模块框架
- 构建和安装系统

### 🚧 开发中
- 实际AI模型集成
- 性能优化
- 测试和验证

### 📋 计划中
- 更多材质类型支持
- 批量处理功能
- 自定义模型训练工具

## 贡献指南

欢迎社区贡献！请遵循以下步骤：

1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue到GitHub仓库
- 参与社区讨论

---

**注意**: 这是一个开源项目，完全免费使用。如果您觉得有用，请给项目点个星！⭐
