#!/bin/bash

# Live2D Material Separation - UXP Plugin Installation
# Based on successful alpaca plugin structure

set -e

# Define colors
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m"

echo -e "${BLUE}🎉 Installing Live2D Material Separation - UXP Plugin${NC}"
echo -e "${BLUE}====================================================${NC}"
echo ""

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
UXP_SOURCE="$SCRIPT_DIR/UXP-Plugin"
PS_PLUGINS_DIR="/Applications/Adobe Photoshop 2025/Plug-ins"
PLUGIN_NAME="Live2D-MaterialSeparation"
PLUGIN_DEST="$PS_PLUGINS_DIR/$PLUGIN_NAME"

# Check if Photoshop exists
if [ ! -d "/Applications/Adobe Photoshop 2025" ]; then
    echo -e "${RED}❌ Adobe Photoshop 2025 not found${NC}"
    echo -e "${YELLOW}Checking for other versions...${NC}"
    
    for version in 2024 2023 2022; do
        if [ -d "/Applications/Adobe Photoshop $version" ]; then
            PS_PLUGINS_DIR="/Applications/Adobe Photoshop $version/Plug-ins"
            PLUGIN_DEST="$PS_PLUGINS_DIR/$PLUGIN_NAME"
            echo -e "${GREEN}✅ Found Adobe Photoshop $version${NC}"
            break
        fi
    done
    
    if [ ! -d "$PS_PLUGINS_DIR" ]; then
        echo -e "${RED}❌ No compatible Photoshop version found${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Target directory: $PS_PLUGINS_DIR${NC}"

# Check if UXP plugin source exists
if [ ! -d "$UXP_SOURCE" ]; then
    echo -e "${RED}❌ UXP plugin source not found: $UXP_SOURCE${NC}"
    exit 1
fi

echo -e "${GREEN}✅ UXP plugin source found${NC}"

# Remove existing installation
if [ -d "$PLUGIN_DEST" ]; then
    echo -e "${YELLOW}Removing existing installation...${NC}"
    sudo rm -rf "$PLUGIN_DEST"
fi

# Create plugin directory
echo -e "${YELLOW}Creating plugin directory...${NC}"
sudo mkdir -p "$PLUGIN_DEST"

# Copy UXP plugin files
echo -e "${YELLOW}Copying UXP plugin files...${NC}"
sudo cp "$UXP_SOURCE/manifest.json" "$PLUGIN_DEST/"
sudo cp "$UXP_SOURCE/index.html" "$PLUGIN_DEST/"

# Copy icons
if [ -d "$UXP_SOURCE/icons" ]; then
    sudo cp -R "$UXP_SOURCE/icons" "$PLUGIN_DEST/"
else
    echo -e "${YELLOW}⚠️ Icons directory not found, creating placeholder...${NC}"
    sudo mkdir -p "$PLUGIN_DEST/icons"
    # Create simple placeholder icons
    sudo touch "$PLUGIN_DEST/icons/<EMAIL>"
    sudo touch "$PLUGIN_DEST/icons/<EMAIL>"
fi

# Set permissions
echo -e "${YELLOW}Setting permissions...${NC}"
sudo chmod -R 755 "$PLUGIN_DEST"

# Verify installation
echo -e "${YELLOW}Verifying installation...${NC}"
if [ -f "$PLUGIN_DEST/manifest.json" ] && [ -f "$PLUGIN_DEST/index.html" ]; then
    echo -e "${GREEN}✅ Installation successful!${NC}"
else
    echo -e "${RED}❌ Installation verification failed${NC}"
    exit 1
fi

# Show installation info
echo -e "\n${GREEN}==================================================${NC}"
echo -e "${GREEN}  Live2D Material Separation Plugin Installed!${NC}"
echo -e "${GREEN}==================================================${NC}"
echo -e "\n${YELLOW}Plugin installed to:${NC}"
echo -e "${BLUE}$PLUGIN_DEST${NC}"
echo -e "\n${YELLOW}Files installed:${NC}"
ls -la "$PLUGIN_DEST"

echo -e "\n${YELLOW}Next Steps:${NC}"
echo -e "1. ${BLUE}Completely restart Photoshop${NC} (Cmd+Q then reopen)"
echo -e "2. ${BLUE}Look for the plugin in:${NC}"
echo -e "   • Window > Extensions > Live2D Material Separation"
echo -e "   • Or in the plugin panel list"
echo -e "3. ${BLUE}If not visible, check:${NC}"
echo -e "   • Photoshop > Preferences > Plug-ins"
echo -e "   • Enable 'Load Extension Panels'"

echo -e "\n${GREEN}🎉 Installation complete! Enjoy your license-free Live2D tool!${NC}"

# Create test script
cat > "$SCRIPT_DIR/test_uxp_installation.sh" << 'EOF'
#!/bin/bash

echo "🧪 Testing UXP Plugin Installation"
echo "=================================="

PLUGIN_DIR="/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"

# Check for different PS versions
for version in 2025 2024 2023; do
    TEST_DIR="/Applications/Adobe Photoshop $version/Plug-ins/Live2D-MaterialSeparation"
    if [ -d "$TEST_DIR" ]; then
        PLUGIN_DIR="$TEST_DIR"
        echo "✅ Found plugin in Photoshop $version"
        break
    fi
done

if [ -d "$PLUGIN_DIR" ]; then
    echo "✅ Plugin directory exists: $PLUGIN_DIR"
    
    # Check files
    if [ -f "$PLUGIN_DIR/manifest.json" ]; then
        echo "✅ manifest.json found"
        echo "📋 Plugin info:"
        grep -E '"name"|"version"|"id"' "$PLUGIN_DIR/manifest.json"
    else
        echo "❌ manifest.json missing"
    fi
    
    if [ -f "$PLUGIN_DIR/index.html" ]; then
        echo "✅ index.html found"
    else
        echo "❌ index.html missing"
    fi
    
    if [ -d "$PLUGIN_DIR/icons" ]; then
        echo "✅ icons directory found"
    else
        echo "❌ icons directory missing"
    fi
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Restart Photoshop completely"
    echo "2. Look for 'Live2D Material Separation' in:"
    echo "   • Window > Extensions"
    echo "   • Plugin panel list"
    echo "3. If not visible, enable 'Load Extension Panels' in Preferences"
    
else
    echo "❌ Plugin not found"
    echo "Run ./install_uxp_plugin.sh to install"
fi
EOF

chmod +x "$SCRIPT_DIR/test_uxp_installation.sh"
echo -e "\n${BLUE}💡 Test installation: ./test_uxp_installation.sh${NC}"

exit 0
