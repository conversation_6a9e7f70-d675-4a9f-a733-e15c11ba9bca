#!/bin/bash

# Live2D Material Separation Plugin - Direct Photoshop Installation
# Installs plugin directly to Photoshop Plug-ins directory

set -e

# Define colors
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m"

echo -e "${BLUE}🔧 Installing Live2D Material Separation to Photoshop${NC}"
echo -e "${BLUE}===================================================${NC}"
echo ""

# Check if Photoshop exists
PS_DIR="/Applications/Adobe Photoshop 2025"
if [ ! -d "$PS_DIR" ]; then
    echo -e "${RED}❌ Adobe Photoshop 2025 not found at: $PS_DIR${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found Adobe Photoshop 2025${NC}"

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PS_PLUGINS_DIR="$PS_DIR/Plug-ins"
PLUGIN_DIR="$PS_PLUGINS_DIR/Live2D-MaterialSeparation"

echo -e "${YELLOW}Plugin will be installed to: $PLUGIN_DIR${NC}"

# Request sudo access
echo -e "${YELLOW}Administrator access required to install to Photoshop directory...${NC}"
sudo -v

# Remove existing installation
if [ -d "$PLUGIN_DIR" ]; then
    echo -e "${YELLOW}Removing existing installation...${NC}"
    sudo rm -rf "$PLUGIN_DIR"
fi

# Create plugin directory
echo -e "${YELLOW}Creating plugin directory...${NC}"
sudo mkdir -p "$PLUGIN_DIR"

# Copy all source files
echo -e "${YELLOW}Copying plugin files...${NC}"
sudo cp -R "$SCRIPT_DIR/Source/"* "$PLUGIN_DIR/"

# Copy additional files
sudo cp "$SCRIPT_DIR/requirements.txt" "$PLUGIN_DIR/" 2>/dev/null || true
sudo cp "$SCRIPT_DIR/README.md" "$PLUGIN_DIR/" 2>/dev/null || true

# Create a simple launcher script
echo -e "${YELLOW}Creating launcher script...${NC}"
sudo tee "$PLUGIN_DIR/launch.sh" > /dev/null << 'EOF'
#!/bin/bash
# Live2D Material Separation Plugin Launcher

PLUGIN_DIR="$(dirname "$0")"
cd "$PLUGIN_DIR"

# Check if Python module exists
if [ -f "Python/material_separation.py" ]; then
    echo "Starting Live2D Material Separation..."
    python3 Python/material_separation.py "$@"
else
    echo "Error: Python module not found"
    exit 1
fi
EOF

# Set permissions
echo -e "${YELLOW}Setting permissions...${NC}"
sudo chmod -R 755 "$PLUGIN_DIR"
sudo chmod +x "$PLUGIN_DIR/launch.sh"

# Create Info.plist for plugin recognition
echo -e "${YELLOW}Creating plugin info file...${NC}"
sudo tee "$PLUGIN_DIR/Info.plist" > /dev/null << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleIdentifier</key>
    <string>com.live2d.materialseparation</string>
    <key>CFBundleName</key>
    <string>Live2D Material Separation</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleDisplayName</key>
    <string>Live2D Material Separation</string>
    <key>PhotoshopFilterCategory</key>
    <string>Live2D</string>
</dict>
</plist>
EOF

# Create a simple test script
cat > "$SCRIPT_DIR/test_photoshop_plugin.sh" << 'EOF'
#!/bin/bash

echo "🧪 Testing Photoshop Plugin Installation"
echo "======================================="

PLUGIN_DIR="/Applications/Adobe Photoshop 2025/Plug-ins/Live2D-MaterialSeparation"

if [ -d "$PLUGIN_DIR" ]; then
    echo "✅ Plugin directory exists"
    
    # List contents
    echo "📁 Plugin contents:"
    ls -la "$PLUGIN_DIR"
    
    # Check key files
    if [ -f "$PLUGIN_DIR/Python/material_separation.py" ]; then
        echo "✅ Python module found"
    else
        echo "❌ Python module missing"
    fi
    
    if [ -f "$PLUGIN_DIR/Info.plist" ]; then
        echo "✅ Info.plist found"
    else
        echo "❌ Info.plist missing"
    fi
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Restart Photoshop completely"
    echo "2. Look in Filter menu for Live2D category"
    echo "3. Or check Window > Extensions for CEP version"
    echo "4. Check Photoshop > Preferences > Plug-ins"
    
else
    echo "❌ Plugin not installed"
    echo "Run ./install_to_photoshop.sh to install"
fi
EOF

chmod +x "$SCRIPT_DIR/test_photoshop_plugin.sh"

# Final message
echo -e "\n${GREEN}==================================================${NC}"
echo -e "${GREEN}  Installation Complete!${NC}"
echo -e "${GREEN}==================================================${NC}"
echo -e "\n${YELLOW}Plugin installed to:${NC}"
echo -e "${BLUE}$PLUGIN_DIR${NC}"
echo -e "\n${YELLOW}Next Steps:${NC}"
echo -e "1. ${BLUE}Completely restart Photoshop${NC} (Cmd+Q then reopen)"
echo -e "2. ${BLUE}Check Filter menu${NC} for Live2D category"
echo -e "3. ${BLUE}Check Window > Extensions${NC} for panel version"
echo -e "4. ${BLUE}Check Preferences > Plug-ins${NC} for plugin list"
echo -e "\n${YELLOW}Test installation:${NC}"
echo -e "${BLUE}./test_photoshop_plugin.sh${NC}"
echo -e "\n${GREEN}The plugin should now appear in Photoshop!${NC}"

exit 0
