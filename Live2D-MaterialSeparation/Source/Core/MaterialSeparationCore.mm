/**
 * Live2D Material Separation Plugin for macOS
 * Core Processing Module - Objective-C++ Implementation
 * Open Source Version - No License Verification
 */

#import <Foundation/Foundation.h>
#import <Metal/Metal.h>
#import <MetalPerformanceShaders/MetalPerformanceShaders.h>
#import <Accelerate/Accelerate.h>
#include <vector>
#include <string>
#include <memory>

// Interface to Python modules
#include <Python.h>

// Class declaration
@interface MaterialSeparationCore : NSObject

// Properties
@property (nonatomic, strong) id<MTLDevice> device;
@property (nonatomic, strong) id<MTLCommandQueue> commandQueue;
@property (nonatomic, assign) PyObject* pythonModule;
@property (nonatomic, assign) BOOL isInitialized;

// Methods
- (instancetype)init;
- (BOOL)initializePython;
- (BOOL)loadModels:(NSString*)modelPath;
- (NSDictionary*)processImage:(NSString*)imagePath withQuality:(float)quality detailLevel:(float)detailLevel;
- (void)cleanup;

@end

// Implementation
@implementation MaterialSeparationCore

- (instancetype)init {
    self = [super init];
    if (self) {
        // Initialize Metal
        _device = MTLCreateSystemDefaultDevice();
        if (!_device) {
            NSLog(@"Error: Could not create Metal device");
            return nil;
        }
        
        _commandQueue = [_device newCommandQueue];
        if (!_commandQueue) {
            NSLog(@"Error: Could not create command queue");
            return nil;
        }
        
        // Initialize Python
        _isInitialized = [self initializePython];
    }
    return self;
}

- (BOOL)initializePython {
    // Initialize Python interpreter
    Py_Initialize();
    if (!Py_IsInitialized()) {
        NSLog(@"Error: Failed to initialize Python interpreter");
        return NO;
    }
    
    // Add our module path to Python's path
    NSString *pythonPath = [[NSBundle mainBundle] pathForResource:@"Python" ofType:nil];
    PyObject *sysPath = PySys_GetObject("path");
    PyObject *path = PyUnicode_FromString([pythonPath UTF8String]);
    PyList_Append(sysPath, path);
    Py_DECREF(path);
    
    // Import our main Python module
    PyObject *pName = PyUnicode_FromString("material_separation");
    _pythonModule = PyImport_Import(pName);
    Py_DECREF(pName);
    
    if (!_pythonModule) {
        NSLog(@"Error: Failed to import Python module");
        PyErr_Print();
        return NO;
    }
    
    return YES;
}

- (BOOL)loadModels:(NSString*)modelPath {
    if (!_isInitialized) {
        NSLog(@"Error: Core not initialized");
        return NO;
    }
    
    // Call Python function to load models
    PyObject *pFunc = PyObject_GetAttrString(_pythonModule, "load_models");
    if (!pFunc || !PyCallable_Check(pFunc)) {
        NSLog(@"Error: Cannot find Python function 'load_models'");
        PyErr_Print();
        return NO;
    }
    
    PyObject *pArgs = PyTuple_New(1);
    PyObject *pModelPath = PyUnicode_FromString([modelPath UTF8String]);
    PyTuple_SetItem(pArgs, 0, pModelPath); // pArgs steals reference to pModelPath
    
    PyObject *pResult = PyObject_CallObject(pFunc, pArgs);
    Py_DECREF(pArgs);
    Py_DECREF(pFunc);
    
    if (!pResult) {
        NSLog(@"Error: Model loading failed");
        PyErr_Print();
        return NO;
    }
    
    BOOL success = PyObject_IsTrue(pResult);
    Py_DECREF(pResult);
    
    return success;
}

- (NSDictionary*)processImage:(NSString*)imagePath withQuality:(float)quality detailLevel:(float)detailLevel {
    if (!_isInitialized) {
        NSLog(@"Error: Core not initialized");
        return nil;
    }
    
    // Call Python function to process image
    PyObject *pFunc = PyObject_GetAttrString(_pythonModule, "process_image");
    if (!pFunc || !PyCallable_Check(pFunc)) {
        NSLog(@"Error: Cannot find Python function 'process_image'");
        PyErr_Print();
        return nil;
    }
    
    PyObject *pArgs = PyTuple_New(3);
    PyObject *pImagePath = PyUnicode_FromString([imagePath UTF8String]);
    PyObject *pQuality = PyFloat_FromDouble(quality);
    PyObject *pDetailLevel = PyFloat_FromDouble(detailLevel);
    
    PyTuple_SetItem(pArgs, 0, pImagePath); // pArgs steals reference
    PyTuple_SetItem(pArgs, 1, pQuality);
    PyTuple_SetItem(pArgs, 2, pDetailLevel);
    
    PyObject *pResult = PyObject_CallObject(pFunc, pArgs);
    Py_DECREF(pArgs);
    Py_DECREF(pFunc);
    
    if (!pResult) {
        NSLog(@"Error: Image processing failed");
        PyErr_Print();
        return nil;
    }
    
    // Convert Python dict to NSDictionary
    NSMutableDictionary *resultDict = [NSMutableDictionary dictionary];
    
    PyObject *pKey, *pValue;
    Py_ssize_t pos = 0;
    
    while (PyDict_Next(pResult, &pos, &pKey, &pValue)) {
        NSString *key = [NSString stringWithUTF8String:PyUnicode_AsUTF8(pKey)];
        
        if (PyUnicode_Check(pValue)) {
            NSString *value = [NSString stringWithUTF8String:PyUnicode_AsUTF8(pValue)];
            resultDict[key] = value;
        }
        else if (PyFloat_Check(pValue)) {
            double value = PyFloat_AsDouble(pValue);
            resultDict[key] = @(value);
        }
        else if (PyLong_Check(pValue)) {
            long value = PyLong_AsLong(pValue);
            resultDict[key] = @(value);
        }
        else if (PyBool_Check(pValue)) {
            BOOL value = (pValue == Py_True);
            resultDict[key] = @(value);
        }
    }
    
    Py_DECREF(pResult);
    
    return resultDict;
}

- (void)cleanup {
    if (_pythonModule) {
        Py_DECREF(_pythonModule);
        _pythonModule = NULL;
    }
    
    Py_Finalize();
    _isInitialized = NO;
}

- (void)dealloc {
    [self cleanup];
}

@end

// C interface for the dylib
extern "C" {
    
    // Create a new instance of the core
    void* MaterialSeparation_Create() {
        @autoreleasepool {
            MaterialSeparationCore *core = [[MaterialSeparationCore alloc] init];
            return (__bridge_retained void*)core;
        }
    }
    
    // Load models
    bool MaterialSeparation_LoadModels(void* instance, const char* modelPath) {
        @autoreleasepool {
            MaterialSeparationCore *core = (__bridge MaterialSeparationCore*)instance;
            NSString *path = [NSString stringWithUTF8String:modelPath];
            return [core loadModels:path];
        }
    }
    
    // Process an image
    const char* MaterialSeparation_ProcessImage(void* instance, const char* imagePath, float quality, float detailLevel) {
        @autoreleasepool {
            MaterialSeparationCore *core = (__bridge MaterialSeparationCore*)instance;
            NSString *path = [NSString stringWithUTF8String:imagePath];
            NSDictionary *result = [core processImage:path withQuality:quality detailLevel:detailLevel];
            
            if (!result) {
                return NULL;
            }
            
            // Convert result to JSON
            NSError *error = nil;
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:result options:0 error:&error];
            
            if (error) {
                NSLog(@"Error converting result to JSON: %@", error);
                return NULL;
            }
            
            NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            return strdup([jsonString UTF8String]);
        }
    }
    
    // Destroy the instance
    void MaterialSeparation_Destroy(void* instance) {
        @autoreleasepool {
            if (instance) {
                MaterialSeparationCore *core = (__bridge_transfer MaterialSeparationCore*)instance;
                [core cleanup];
            }
        }
    }
}
