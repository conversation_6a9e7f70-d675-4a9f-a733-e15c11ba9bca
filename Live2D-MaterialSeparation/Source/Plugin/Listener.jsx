/*
 * Live2D Material Separation Plugin for macOS
 * Photoshop JSX Script
 * Open Source Version - No License Verification
 */

// Constants
var PLUGIN_ID = "com.live2d.materialseparation";
var PLUGIN_VERSION = "1.0.0";

// Initialize plugin
function init() {
    console.log("Initializing Live2D Material Separation Plugin...");

    // Load UI
    loadUI();

    // Register event listeners
    registerEventListeners();

    // Initialize core module
    initializeCoreModule();

    console.log("Plugin initialized successfully");
}

// Load user interface
function loadUI() {
    // Create main panel
    var mainPanel = new Window("dialog", "Live2D Material Separation");
    mainPanel.orientation = "column";
    mainPanel.alignChildren = "fill";

    // Add UI components
    var imagePanel = mainPanel.add("panel", undefined, "Image");
    var settingsPanel = mainPanel.add("panel", undefined, "Settings");
    var buttonGroup = mainPanel.add("group");

    // Image selection
    var selectButton = imagePanel.add("button", undefined, "Select Image");
    var imagePathText = imagePanel.add("statictext", undefined, "No image selected");

    // Settings
    var qualitySlider = settingsPanel.add("slider", undefined, 50, 0, 100);
    var qualityValue = settingsPanel.add("statictext", undefined, "Quality: 50%");

    var detailSlider = settingsPanel.add("slider", undefined, 50, 0, 100);
    var detailValue = settingsPanel.add("statictext", undefined, "Detail: 50%");

    // Buttons
    var processButton = buttonGroup.add("button", undefined, "Process");
    var cancelButton = buttonGroup.add("button", undefined, "Cancel");

    // Event handlers
    qualitySlider.onChanging = function() {
        qualityValue.text = "Quality: " + Math.round(this.value) + "%";
    };

    detailSlider.onChanging = function() {
        detailValue.text = "Detail: " + Math.round(this.value) + "%";
    };

    selectButton.onClick = function() {
        // Open file dialog
        var file = File.openDialog("Select an image", "*.psd;*.png;*.jpg");
        if (file) {
            imagePathText.text = file.fsName;
        }
    };

    processButton.onClick = function() {
        if (imagePathText.text === "No image selected") {
            alert("Please select an image first.");
            return;
        }

        // Process image
        processImage(imagePathText.text, Math.round(qualitySlider.value), Math.round(detailSlider.value));
        mainPanel.close();
    };

    cancelButton.onClick = function() {
        mainPanel.close();
    };

    // Show panel
    mainPanel.show();
}

// Register event listeners for Photoshop events
function registerEventListeners() {
    csInterface.addEventListener("com.adobe.csxs.events.documentAfterActivate", function(event) {
        console.log("Document activated: " + event.data);
    });
}

// Initialize core processing module
function initializeCoreModule() {
    try {
        // In a real implementation, this would load the native dylib
        // For this example, we're just simulating the initialization
        console.log("Core module initialized");
        return true;
    } catch (e) {
        console.error("Failed to initialize core module: " + e.message);
        alert("Failed to initialize core module. Please reinstall the plugin.");
        return false;
    }
}

// Process image using AI models
function processImage(imagePath, quality, detail) {
    console.log("Processing image: " + imagePath);
    console.log("Quality: " + quality + ", Detail: " + detail);

    // Show progress dialog
    var progressWin = new Window("palette", "Processing...");
    progressWin.orientation = "column";
    progressWin.alignChildren = "center";

    var progressBar = progressWin.add("progressbar", undefined, 0, 100);
    progressBar.preferredSize.width = 300;
    var statusText = progressWin.add("statictext", undefined, "Initializing...");

    progressWin.show();

    // Simulate processing steps
    setTimeout(function() {
        progressBar.value = 10;
        statusText.text = "Loading models...";

        setTimeout(function() {
            progressBar.value = 30;
            statusText.text = "Analyzing image...";

            setTimeout(function() {
                progressBar.value = 50;
                statusText.text = "Separating materials...";

                setTimeout(function() {
                    progressBar.value = 70;
                    statusText.text = "Refining results...";

                    setTimeout(function() {
                        progressBar.value = 90;
                        statusText.text = "Creating layers...";

                        setTimeout(function() {
                            progressBar.value = 100;
                            statusText.text = "Done!";

                            // Create result in Photoshop
                            createResultLayers();

                            progressWin.close();
                            alert("Material separation completed successfully!");
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }, 500);
    }, 500);
}

// Create material layers function that can be called from JavaScript
function createMaterialLayers(imagePath, settings) {
    try {
        if (!app.documents.length) {
            return "error: No active document";
        }

        var doc = app.activeDocument;

        // Create main group for results
        var mainGroup = doc.layerSets.add();
        mainGroup.name = "Live2D Material Separation";

        // Define materials to create based on settings
        var materials = [];
        if (settings.materials.skin) materials.push("Skin");
        if (settings.materials.hair) materials.push("Hair");
        if (settings.materials.eyes) materials.push("Eyes");
        if (settings.materials.clothes) materials.push("Clothes");
        if (settings.materials.accessories) materials.push("Accessories");
        if (settings.materials.background) materials.push("Background");

        // Create layers for each material
        for (var i = 0; i < materials.length; i++) {
            var layer = mainGroup.artLayers.add();
            layer.name = materials[i];
            layer.blendMode = BlendMode.NORMAL;
            layer.opacity = 100;
        }

        // Add info layer with processing details
        var infoLayer = mainGroup.artLayers.add();
        infoLayer.name = "Processing Info - Quality: " + settings.quality + "%, Detail: " + settings.detailLevel + "%";

        return "success";

    } catch (e) {
        return "error: " + e.message;
    }
}

// Crop function for selection-based processing
function cropSelection(settings) {
    try {
        if (!app.documents.length) {
            return "error: No active document";
        }

        var doc = app.activeDocument;

        // Check if there's a selection
        try {
            var bounds = doc.selection.bounds;
        } catch (e) {
            return "error: No selection found. Please make a selection first.";
        }

        // Create group if needed
        var targetGroup = null;
        if (settings.createGroup) {
            targetGroup = doc.layerSets.add();
            targetGroup.name = "Crop Results";
        }

        // Copy selection to new layer
        doc.selection.copy();
        var croppedLayer = doc.artLayers.add();
        croppedLayer.name = "Cropped Layer";

        // Paste the selection
        doc.paste();

        // Move to group if created
        if (targetGroup) {
            croppedLayer.move(targetGroup, ElementPlacement.INSIDE);
        }

        // Fill the original area if requested
        if (settings.fillCropArea) {
            // Select the original area again
            doc.selection.load(doc.selection);

            // Use content-aware fill
            try {
                // This requires newer Photoshop versions
                executeAction(stringIDToTypeID("fill"), undefined, DialogModes.NO);
            } catch (e) {
                // Fallback to regular fill
                var fillColor = app.foregroundColor;
                doc.selection.fill(fillColor);
            }
        }

        // Clear selection
        doc.selection.deselect();

        return "success";

    } catch (e) {
        return "error: " + e.message;
    }
}

// Expand function for extending transparent areas
function expandSelection(settings) {
    try {
        if (!app.documents.length) {
            return "error: No active document";
        }

        var doc = app.activeDocument;

        // Check if there's a selection
        try {
            var bounds = doc.selection.bounds;
        } catch (e) {
            return "error: No selection found. Please make a selection first.";
        }

        // Create group if needed
        var targetGroup = null;
        if (settings.createGroup) {
            targetGroup = doc.layerSets.add();
            targetGroup.name = "Expand Results";
        }

        // Use content-aware fill to expand the area
        try {
            executeAction(stringIDToTypeID("fill"), undefined, DialogModes.NO);
        } catch (e) {
            // Fallback method
            var expandedLayer = doc.artLayers.add();
            expandedLayer.name = "Expanded Layer";

            if (targetGroup) {
                expandedLayer.move(targetGroup, ElementPlacement.INSIDE);
            }
        }

        // Clear selection
        doc.selection.deselect();

        return "success";

    } catch (e) {
        return "error: " + e.message;
    }
}

// Main entry point
init();
