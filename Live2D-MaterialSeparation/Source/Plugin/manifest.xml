<?xml version="1.0" encoding="UTF-8"?>
<ExtensionManifest Version="6.0" ExtensionBundleId="com.live2d.materialseparation" ExtensionBundleVersion="1.0.0"
				 ExtensionBundleName="Live2D Material Separation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<ExtensionList>
		<Extension Id="com.live2d.materialseparation" Version="1.0.0" />
	</ExtensionList>
	<ExecutionEnvironment>
		<HostList>
			<Host Name="PHSP" Version="[22.0,99.9]" />
			<Host Name="PHXS" Version="[22.0,99.9]" />
		</HostList>
		<LocaleList>
			<Locale Code="All" />
		</LocaleList>
		<RequiredRuntimeList>
			<RequiredRuntime Name="CSXS" Version="9.0" />
		</RequiredRuntimeList>
	</ExecutionEnvironment>
	<DispatchInfoList>
		<Extension Id="com.live2d.materialseparation">
			<DispatchInfo>
				<Resources>
					<MainPath>./index.html</MainPath>
					<ScriptPath>./jsx/Listener.jsx</ScriptPath>
				</Resources>
				<Lifecycle>
					<AutoVisible>true</AutoVisible>
				</Lifecycle>
				<UI>
					<Type>Panel</Type>
					<Menu>Live2D Material Separation</Menu>
					<Geometry>
						<Size>
							<Height>600</Height>
							<Width>400</Width>
						</Size>
						<MinSize>
							<Height>400</Height>
							<Width>300</Width>
						</MinSize>
						<MaxSize>
							<Height>800</Height>
							<Width>600</Width>
						</MaxSize>
					</Geometry>
					<Icons>
						<Icon Type="Normal">./icons/icon_normal.png</Icon>
						<Icon Type="RollOver">./icons/icon_rollover.png</Icon>
						<Icon Type="DarkNormal">./icons/icon_normal_dark.png</Icon>
						<Icon Type="DarkRollOver">./icons/icon_rollover_dark.png</Icon>
					</Icons>
				</UI>
			</DispatchInfo>
		</Extension>
	</DispatchInfoList>
</ExtensionManifest>
