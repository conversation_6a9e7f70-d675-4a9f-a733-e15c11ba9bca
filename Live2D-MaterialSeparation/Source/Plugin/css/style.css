/* Live2D Material Separation Plugin for macOS */
/* Main CSS Styles */

/* Variables */
:root {
    --primary-color: #4a90e2;
    --primary-hover: #3a80d2;
    --secondary-color: #f5f5f7;
    --text-color: #333333;
    --light-text: #666666;
    --border-color: #dddddd;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --background-color: #ffffff;
    --panel-background: #f5f5f7;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #0a84ff;
        --primary-hover: #409cff;
        --secondary-color: #2c2c2e;
        --text-color: #ffffff;
        --light-text: #ebebf0;
        --border-color: #3d3d3d;
        --background-color: #1c1c1e;
        --panel-background: #2c2c2e;
        --shadow-color: rgba(0, 0, 0, 0.3);
    }
}

/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--background-color);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 16px;
}

/* Header */
header {
    margin-bottom: 20px;
}

header h1 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 4px;
}

header .version {
    font-size: 12px;
    color: var(--light-text);
}

/* Main content */
main {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;
}

/* Sections */
section {
    background-color: var(--panel-background);
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

section h2 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

/* Image section */
.image-preview {
    width: 100%;
    height: 150px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    overflow: hidden;
}

#preview-placeholder {
    color: var(--light-text);
    font-size: 14px;
}

#preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.button-group {
    display: flex;
    gap: 8px;
}

/* Settings section */
.setting-item {
    margin-bottom: 16px;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.slider {
    flex: 1;
    -webkit-appearance: none;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    outline: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

#quality-value, #detail-value {
    min-width: 40px;
    text-align: right;
}

.setting-description {
    font-size: 12px;
    color: var(--light-text);
    margin-top: 4px;
}

.dropdown {
    width: 100%;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
    color: var(--text-color);
    font-size: 14px;
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23666" d="M0 0l6 6 6-6z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 10px;
}

/* Materials section */
.materials-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.material-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.material-item input[type="checkbox"] {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    position: relative;
}

.material-item input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.material-item input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 3px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Footer */
footer {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-text {
    font-size: 12px;
    color: var(--light-text);
    text-align: center;
}

/* Buttons */
button {
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    border: none;
    outline: none;
}

button:active {
    transform: scale(0.98);
}

.primary-button {
    background-color: var(--primary-color);
    color: white;
}

.primary-button:hover {
    background-color: var(--primary-hover);
}

.secondary-button {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

.secondary-button:hover {
    background-color: var(--border-color);
}

#process-button {
    width: 100%;
    padding: 12px;
    font-size: 16px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--background-color);
    border-radius: 12px;
    padding: 24px;
    width: 80%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.modal-content h2 {
    margin-bottom: 16px;
    text-align: center;
}

.progress-container {
    margin-bottom: 16px;
}

.progress-bar {
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: right;
    font-size: 12px;
    color: var(--light-text);
}

#progress-status {
    text-align: center;
    margin-bottom: 20px;
}

#cancel-button {
    display: block;
    margin: 0 auto;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--light-text);
}
