#!/bin/bash

# Live2D Material Separation Plugin for macOS
# Installation Script
# Open Source Version - No License Verification

# Set error handling
set -e

# Define colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PS_PLUGIN_DIR="$HOME/Library/Application Support/Adobe/Adobe Photoshop/Plug-ins"
PS_CEP_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions"
EXTENSION_ID="com.live2d.materialseparation"

# Print banner
echo -e "${GREEN}==================================================${NC}"
echo -e "${GREEN}  Live2D Material Separation Plugin Installer${NC}"
echo -e "${GREEN}  Open Source Version for macOS${NC}"
echo -e "${GREEN}==================================================${NC}"
echo ""

# Check if Python 3.10+ is installed
echo -e "${YELLOW}Checking Python installation...${NC}"
if command -v python3 >/dev/null 2>&1; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -ge 3 ] && [ "$PYTHON_MINOR" -ge 10 ]; then
        echo -e "${GREEN}✓ Python $PYTHON_VERSION detected${NC}"
    else
        echo -e "${RED}✗ Python 3.10+ required, but $PYTHON_VERSION found${NC}"
        echo -e "${YELLOW}Please install Python 3.10 or newer:${NC}"
        echo "brew install python@3.10"
        exit 1
    fi
else
    echo -e "${RED}✗ Python 3 not found${NC}"
    echo -e "${YELLOW}Please install Python 3.10 or newer:${NC}"
    echo "brew install python@3.10"
    exit 1
fi

# Check if Photoshop is installed
echo -e "${YELLOW}Checking Adobe Photoshop installation...${NC}"
if [ -d "/Applications/Adobe Photoshop 2024" ]; then
    echo -e "${GREEN}✓ Adobe Photoshop 2024 detected${NC}"
    PS_VERSION="2024"
elif [ -d "/Applications/Adobe Photoshop 2023" ]; then
    echo -e "${GREEN}✓ Adobe Photoshop 2023 detected${NC}"
    PS_VERSION="2023"
elif [ -d "/Applications/Adobe Photoshop 2022" ]; then
    echo -e "${GREEN}✓ Adobe Photoshop 2022 detected${NC}"
    PS_VERSION="2022"
elif [ -d "/Applications/Adobe Photoshop 2021" ]; then
    echo -e "${GREEN}✓ Adobe Photoshop 2021 detected${NC}"
    PS_VERSION="2021"
else
    echo -e "${RED}✗ Adobe Photoshop not found${NC}"
    echo -e "${YELLOW}Please install Adobe Photoshop 2021 or newer${NC}"
    read -p "Continue anyway? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    PS_VERSION="2024"
fi

# Create virtual environment if it doesn't exist
echo -e "${YELLOW}Setting up Python virtual environment...${NC}"
if [ ! -d "$SCRIPT_DIR/venv" ]; then
    python3 -m venv "$SCRIPT_DIR/venv"
    echo -e "${GREEN}✓ Created virtual environment${NC}"
else
    echo -e "${GREEN}✓ Virtual environment already exists${NC}"
fi

# Activate virtual environment
source "$SCRIPT_DIR/venv/bin/activate"

# Install Python dependencies
echo -e "${YELLOW}Installing Python dependencies...${NC}"
pip install --upgrade pip
pip install -r "$SCRIPT_DIR/requirements.txt"
echo -e "${GREEN}✓ Python dependencies installed${NC}"

# Create Photoshop plugin directories if they don't exist
echo -e "${YELLOW}Setting up Photoshop plugin directories...${NC}"
mkdir -p "$PS_PLUGIN_DIR"
mkdir -p "$PS_CEP_DIR/$EXTENSION_ID"

# Copy plugin files
echo -e "${YELLOW}Copying plugin files...${NC}"

# Copy core library
cp -f "$SCRIPT_DIR/Source/Core/MaterialSeparationCore.dylib" "$PS_PLUGIN_DIR/"

# Copy CEP extension files
cp -R "$SCRIPT_DIR/Source/Plugin/"* "$PS_CEP_DIR/$EXTENSION_ID/"

# Create symbolic link to Python environment
ln -sf "$SCRIPT_DIR/Source/Python" "$PS_CEP_DIR/$EXTENSION_ID/python"

# Copy models
echo -e "${YELLOW}Copying AI models...${NC}"
mkdir -p "$PS_CEP_DIR/$EXTENSION_ID/models"
cp -R "$SCRIPT_DIR/Models/"* "$PS_CEP_DIR/$EXTENSION_ID/models/"

# Set permissions
echo -e "${YELLOW}Setting permissions...${NC}"
chmod -R 755 "$PS_CEP_DIR/$EXTENSION_ID"
chmod 755 "$PS_PLUGIN_DIR/MaterialSeparationCore.dylib"

# Create debug file for CEP
echo -e "${YELLOW}Creating CEP debug file...${NC}"
DEBUG_FOLDER="$HOME/Library/Logs/CSXS"
mkdir -p "$DEBUG_FOLDER"
cat > "$HOME/Library/Preferences/com.adobe.CSXS.10.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>LogLevel</key>
	<string>1</string>
	<key>PlayerDebugMode</key>
	<string>1</string>
</dict>
</plist>
EOF

# Final message
echo -e "\n${GREEN}==================================================${NC}"
echo -e "${GREEN}  Installation Complete!${NC}"
echo -e "${GREEN}==================================================${NC}"
echo -e "\nThe Live2D Material Separation Plugin has been installed."
echo -e "Please restart Adobe Photoshop if it's currently running."
echo -e "\nTo use the plugin in Photoshop, go to:\n"
echo -e "  Filters > Live2D > Material Separation"
echo -e "\nIf you encounter any issues, please check the logs at:\n"
echo -e "  $DEBUG_FOLDER"
echo -e "\nThank you for using the open source version of this plugin!\n"

exit 0
