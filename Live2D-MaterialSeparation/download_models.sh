#!/bin/bash

# Live2D Material Separation Plugin for macOS
# Model Download Script
# Open Source Version - No License Verification

# Set error handling
set -e

# Define colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
MODELS_DIR="$SCRIPT_DIR/Models"

# Create models directory if it doesn't exist
mkdir -p "$MODELS_DIR"

# Print banner
echo -e "${GREEN}==================================================${NC}"
echo -e "${GREEN}  Live2D Material Separation Model Downloader${NC}"
echo -e "${GREEN}  Open Source Version for macOS${NC}"
echo -e "${GREEN}==================================================${NC}"
echo ""

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    echo -e "${RED}Error: curl is not installed. Please install curl to continue.${NC}"
    exit 1
fi

# Function to download a file with progress bar
download_file() {
    local url=$1
    local output_file=$2
    local description=$3
    
    echo -e "${YELLOW}Downloading $description...${NC}"
    curl -L --progress-bar "$url" -o "$output_file"
    
    if [ $? -eq 0 ] && [ -f "$output_file" ]; then
        echo -e "${GREEN}✓ Downloaded $description successfully${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to download $description${NC}"
        return 1
    fi
}

# Function to extract a compressed file
extract_file() {
    local file=$1
    local output_dir=$2
    local description=$3
    
    echo -e "${YELLOW}Extracting $description...${NC}"
    
    # Check file extension and use appropriate extraction method
    if [[ "$file" == *.zip ]]; then
        unzip -q "$file" -d "$output_dir"
    elif [[ "$file" == *.tar.gz ]] || [[ "$file" == *.tgz ]]; then
        tar -xzf "$file" -C "$output_dir"
    elif [[ "$file" == *.tar ]]; then
        tar -xf "$file" -C "$output_dir"
    else
        echo -e "${RED}✗ Unknown archive format for $file${NC}"
        return 1
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Extracted $description successfully${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to extract $description${NC}"
        return 1
    fi
}

# Model URLs
# In a real implementation, these would be actual URLs to the model files
CONVNEXT_URL="https://github.com/facebookresearch/ConvNeXt/releases/download/v0.1/convnext_tiny_1k_224_ema.pth"
DEEPLABV3_URL="https://github.com/VainF/DeepLabV3Plus-Pytorch/releases/download/1.2.0/deeplabv3plus_mobilenet_cityscapes_best.pth"
LAMA_URL="https://github.com/advimman/lama/releases/download/v1.0/big-lama.zip"

# Download and extract models
echo -e "${YELLOW}Starting model downloads...${NC}"

# Download ConvNeXt model
CONVNEXT_FILE="$MODELS_DIR/convnext_tiny_1k_224_ema.pth"
if [ -f "$CONVNEXT_FILE" ]; then
    echo -e "${GREEN}✓ ConvNeXt model already exists${NC}"
else
    download_file "$CONVNEXT_URL" "$CONVNEXT_FILE" "ConvNeXt model"
fi

# Download DeepLabv3 model
DEEPLABV3_FILE="$MODELS_DIR/deeplabv3plus_mobilenet_cityscapes_best.pth"
if [ -f "$DEEPLABV3_FILE" ]; then
    echo -e "${GREEN}✓ DeepLabv3 model already exists${NC}"
else
    download_file "$DEEPLABV3_URL" "$DEEPLABV3_FILE" "DeepLabv3 model"
fi

# Download and extract LaMa model
LAMA_ZIP="$MODELS_DIR/big-lama.zip"
LAMA_DIR="$MODELS_DIR/lama"
if [ -d "$LAMA_DIR" ]; then
    echo -e "${GREEN}✓ LaMa model already exists${NC}"
else
    if download_file "$LAMA_URL" "$LAMA_ZIP" "LaMa model"; then
        mkdir -p "$LAMA_DIR"
        extract_file "$LAMA_ZIP" "$LAMA_DIR" "LaMa model"
        # Clean up zip file after extraction
        rm -f "$LAMA_ZIP"
    fi
 fi

# Create a dummy model file for testing
echo -e "${YELLOW}Creating dummy model file for testing...${NC}"
cat > "$MODELS_DIR/model_info.json" << EOF
{
  "models": {
    "convnext": {
      "name": "ConvNeXt Tiny",
      "version": "1.0",
      "file": "convnext_tiny_1k_224_ema.pth",
      "type": "feature_extractor",
      "input_size": [224, 224],
      "channels": 3
    },
    "deeplabv3": {
      "name": "DeepLabv3+",
      "version": "1.2.0",
      "file": "deeplabv3plus_mobilenet_cityscapes_best.pth",
      "type": "segmentation",
      "input_size": [512, 512],
      "channels": 3,
      "num_classes": 6
    },
    "lama": {
      "name": "LaMa",
      "version": "1.0",
      "file": "lama/big-lama.pth",
      "type": "inpainting",
      "input_size": [512, 512],
      "channels": 4
    }
  },
  "last_updated": "$(date +"%Y-%m-%d")"
}
EOF

echo -e "${GREEN}✓ Created model info file${NC}"

# Final message
echo -e "\n${GREEN}==================================================${NC}"
echo -e "${GREEN}  Model Download Complete!${NC}"
echo -e "${GREEN}==================================================${NC}"
echo -e "\nAll required models have been downloaded to:\n"
echo -e "  $MODELS_DIR\n"
echo -e "You can now proceed with installing the plugin.\n"

exit 0
