#!/bin/bash

# Live2D Material Separation Plugin for macOS
# Build Script
# Open Source Version - No License Verification

# Set error handling
set -e

# Define colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

# Define paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SOURCE_DIR="$SCRIPT_DIR/Source"
BUILD_DIR="$SCRIPT_DIR/Build"
OUTPUT_DIR="$BUILD_DIR/Output"

# Print banner
echo -e "${GREEN}==================================================${NC}"
echo -e "${GREEN}  Live2D Material Separation Plugin Builder${NC}"
echo -e "${GREEN}  Open Source Version for macOS${NC}"
echo -e "${GREEN}==================================================${NC}"
echo ""

# Check if Xcode is installed
if ! xcode-select -p &> /dev/null; then
    echo -e "${RED}Error: Xcode command line tools are not installed.${NC}"
    echo -e "${YELLOW}Please install Xcode command line tools:${NC}"
    echo "xcode-select --install"
    exit 1
fi

# Check if Python 3.10+ is installed
echo -e "${YELLOW}Checking Python installation...${NC}"
if command -v python3 >/dev/null 2>&1; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -ge 3 ] && [ "$PYTHON_MINOR" -ge 10 ]; then
        echo -e "${GREEN}✓ Python $PYTHON_VERSION detected${NC}"
    else
        echo -e "${RED}✗ Python 3.10+ required, but $PYTHON_VERSION found${NC}"
        echo -e "${YELLOW}Please install Python 3.10 or newer:${NC}"
        echo "brew install python@3.10"
        exit 1
    fi
else
    echo -e "${RED}✗ Python 3 not found${NC}"
    echo -e "${YELLOW}Please install Python 3.10 or newer:${NC}"
    echo "brew install python@3.10"
    exit 1
fi

# Create build directories
echo -e "${YELLOW}Creating build directories...${NC}"
mkdir -p "$BUILD_DIR"
mkdir -p "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR/Plugin"
mkdir -p "$OUTPUT_DIR/Core"
mkdir -p "$OUTPUT_DIR/Python"
mkdir -p "$OUTPUT_DIR/Models"

# Build Core library
echo -e "${YELLOW}Building Core library...${NC}"
cd "$SOURCE_DIR/Core"

# Create a simple Makefile for the Core library
cat > Makefile << EOF
CXX = clang++
CFLAGS = -std=c++17 -Wall -Wextra -O3 -fPIC
LDFLAGS = -shared -framework Foundation -framework Metal -framework MetalPerformanceShaders -framework Accelerate
PYTHON_INCLUDE = $(python3-config --includes)
PYTHON_LDFLAGS = $(python3-config --ldflags)

TARGET = MaterialSeparationCore.dylib
SRCS = MaterialSeparationCore.mm
OBJS = \$(SRCS:.mm=.o)

all: \$(TARGET)

%.o: %.mm
	\$(CXX) \$(CFLAGS) \$(PYTHON_INCLUDE) -c \$< -o \$@

\$(TARGET): \$(OBJS)
	\$(CXX) \$(LDFLAGS) \$(PYTHON_LDFLAGS) -o \$@ \$^

clean:
	rm -f \$(OBJS) \$(TARGET)

.PHONY: all clean
EOF

# Build the Core library
make clean || true
make

# Check if build was successful
if [ -f "MaterialSeparationCore.dylib" ]; then
    echo -e "${GREEN}✓ Core library built successfully${NC}"
    cp "MaterialSeparationCore.dylib" "$OUTPUT_DIR/Core/"
else
    echo -e "${RED}✗ Failed to build Core library${NC}"
    exit 1
fi

# Copy Plugin files
echo -e "${YELLOW}Copying Plugin files...${NC}"
cp -R "$SOURCE_DIR/Plugin/"* "$OUTPUT_DIR/Plugin/"

# Copy Python files
echo -e "${YELLOW}Copying Python files...${NC}"
cp -R "$SOURCE_DIR/Python/"* "$OUTPUT_DIR/Python/"

# Create manifest file
echo -e "${YELLOW}Creating manifest file...${NC}"
cat > "$OUTPUT_DIR/manifest.json" << EOF
{
  "id": "com.live2d.materialseparation",
  "name": "Live2D Material Separation",
  "version": "1.0.0",
  "description": "Automatically separate Live2D character materials using AI",
  "author": "Open Source Community",
  "license": "MIT",
  "platform": "macos",
  "min_os_version": "10.15",
  "target_app": "photoshop",
  "min_app_version": "22.0.0",
  "build_date": "$(date +"%Y-%m-%d")"
}
EOF

# Copy installation scripts
echo -e "${YELLOW}Copying installation scripts...${NC}"
cp "$SCRIPT_DIR/install_plugin.sh" "$OUTPUT_DIR/"
cp "$SCRIPT_DIR/download_models.sh" "$OUTPUT_DIR/"
cp "$SCRIPT_DIR/requirements.txt" "$OUTPUT_DIR/"

# Create package script
echo -e "${YELLOW}Creating package script...${NC}"
cat > "$OUTPUT_DIR/package.sh" << EOF
#!/bin/bash

# Package the plugin into a distributable format
cd "\$(dirname "\${BASH_SOURCE[0]}")"
zip -r "../Live2D-MaterialSeparation-macOS.zip" *
echo "Package created: ../Live2D-MaterialSeparation-macOS.zip"
EOF
chmod +x "$OUTPUT_DIR/package.sh"

# Final message
echo -e "\n${GREEN}==================================================${NC}"
echo -e "${GREEN}  Build Complete!${NC}"
echo -e "${GREEN}==================================================${NC}"
echo -e "\nThe plugin has been built successfully.\n"
echo -e "Output files are located at:\n"
echo -e "  $OUTPUT_DIR\n"
echo -e "To install the plugin, run:\n"
echo -e "  cd $OUTPUT_DIR && ./install_plugin.sh\n"

exit 0
