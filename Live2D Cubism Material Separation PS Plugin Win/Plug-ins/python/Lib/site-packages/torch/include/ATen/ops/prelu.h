#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/prelu_ops.h>

namespace at {


// aten::prelu(Tensor self, Tensor weight) -> Tensor
inline at::Tensor prelu(const at::Tensor & self, const at::Tensor & weight) {
    return at::_ops::prelu::call(self, weight);
}

// aten::prelu.out(Tensor self, Tensor weight, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & prelu_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & weight) {
    return at::_ops::prelu_out::call(self, weight, out);
}

// aten::prelu.out(Tensor self, Tensor weight, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & prelu_outf(const at::Tensor & self, const at::Tensor & weight, at::Tensor & out) {
    return at::_ops::prelu_out::call(self, weight, out);
}

}
